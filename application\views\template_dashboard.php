<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="Dashboard - Monitoring DPJP Praktek" />
    <meta name="author" content="" />
    <title><?= isset($title) ? $title : 'Dashboard' ?> - Monitoring DPJP Praktek</title>
    <link href="https://fonts.googleapis.com/css?family=Poppins:400,500,700,800&display=swap" rel="stylesheet">
    <link href="<?= base_url('assets/plugins/bootstrap/css/bootstrap.min.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/plugins/font-awesome/css/all.min.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/plugins/perfectscroll/perfect-scrollbar.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/plugins/DataTables/datatables.min.css') ?>" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css" rel="stylesheet">
    
    <!-- Theme Styles -->
    <link href="<?= base_url('assets/css/main.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/css/custom.css') ?>" rel="stylesheet">
</head>

<style>
    .navbar-brand {
        font-weight: 700;
        font-size: 1.2rem;
    }
    
    .navbar-nav .nav-link {
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        margin: 0 2px;
        transition: all 0.3s ease;
    }
    
    .navbar-nav .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.1);
        transform: translateY(-1px);
    }
    
    .navbar-nav .nav-link.active {
        background-color: rgba(255, 255, 255, 0.2);
        font-weight: 600;
    }
    
    .user-info {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 25px;
        padding: 5px 15px;
        margin-left: 10px;
    }
    
    .logout-btn {
        background: rgba(220, 53, 69, 0.8);
        border: none;
        border-radius: 20px;
        padding: 5px 15px;
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
        margin-left: 10px;
    }
    
    .logout-btn:hover {
        background: rgba(220, 53, 69, 1);
        color: white;
        transform: translateY(-1px);
    }
    
    .main-content {
        min-height: calc(100vh - 76px);
        background: #f8f9fa;
    }
    
    .footer {
        background: #2c3e50;
        color: white;
        padding: 20px 0;
        text-align: center;
        margin-top: auto;
    }
    
    @media (max-width: 768px) {
        .navbar-nav {
            text-align: center;
        }
        
        .user-info, .logout-btn {
            margin: 5px 0;
        }
    }
</style>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <i class="fas fa-hospital me-2"></i>
                Monitoring DPJP Praktek
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('monitoring_pa_cendana') ?>">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('DpjpPraktek') ?>">
                            <i class="fas fa-user-md me-1"></i>
                            DPJP Praktek
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('ketepatandpjp') ?>">
                            <i class="fas fa-clock me-1"></i>
                            Ketepatan DPJP
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('RekapPerDokter') ?>">
                            <i class="fas fa-chart-bar me-1"></i>
                            Rekap Per Dokter
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('Perjanjian') ?>">
                            <i class="fas fa-calendar-check me-1"></i>
                            Data Perjanjian
                        </a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <?php if($this->session->userdata('logged_in')): ?>
                        <div class="user-info">
                            <i class="fas fa-user me-1"></i>
                            <span><?= $this->session->userdata('username') ?></span>
                        </div>
                        <a href="<?= base_url('Auth/logout') ?>" class="logout-btn">
                            <i class="fas fa-sign-out-alt me-1"></i>
                            Logout
                        </a>
                    <?php else: ?>
                        <a href="<?= base_url('Auth') ?>" class="btn btn-outline-light">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            Login
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <?php 
        if(isset($contents)) {
            echo $contents;
        } else {
            echo '<div class="container-fluid py-4">';
            echo '<div class="row">';
            echo '<div class="col-12">';
            echo '<div class="card">';
            echo '<div class="card-body text-center">';
            echo '<h3>Welcome to Monitoring DPJP Praktek</h3>';
            echo '<p class="text-muted">Please select a menu from the navigation above.</p>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
        }
        ?>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?= date('Y') ?> RS PKN Cendana. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Sistem Monitoring DPJP Praktek v1.0</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="<?= base_url('assets/plugins/jquery/jquery-3.4.1.min.js') ?>"></script>
    <script src="<?= base_url('assets/plugins/bootstrap/js/bootstrap.min.js') ?>"></script>
    <script src="<?= base_url('assets/plugins/perfectscroll/perfect-scrollbar.min.js') ?>"></script>
    <script src="<?= base_url('assets/plugins/DataTables/datatables.min.js') ?>"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
    <script src="<?= base_url('assets/js/main.min.js') ?>"></script>

    <script>
        $(document).ready(function() {
            // Set active navigation item based on current URL
            var currentUrl = window.location.pathname;
            $('.navbar-nav .nav-link').each(function() {
                var linkUrl = $(this).attr('href');
                if (currentUrl.includes(linkUrl.split('/').pop())) {
                    $(this).addClass('active');
                }
            });
            
            // Logout confirmation
            $('.logout-btn').on('click', function(e) {
                if (!confirm('Are you sure you want to logout?')) {
                    e.preventDefault();
                }
            });
            
            // Auto-hide alerts
            $('.alert').delay(5000).fadeOut();
        });
    </script>
</body>
</html>
