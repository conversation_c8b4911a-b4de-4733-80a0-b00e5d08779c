<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="Login - Monitoring DPJP Praktek" />
    <meta name="author" content="" />
    <title>Login - Monitoring DPJP Praktek</title>
    <link href="https://fonts.googleapis.com/css?family=Poppins:400,500,700,800&display=swap" rel="stylesheet">
    <link href="assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/plugins/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="assets/css/main.css" rel="stylesheet">
</head>

<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Poppins', sans-serif;
    }

    .login-container {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        max-width: 400px;
        width: 100%;
    }

    .login-header {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }

    .login-header h2 {
        margin: 0;
        font-weight: 600;
        font-size: 1.8rem;
    }

    .login-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
        font-size: 0.9rem;
    }

    .login-body {
        padding: 40px 30px;
    }

    .form-group {
        margin-bottom: 25px;
        position: relative;
    }

    .form-control {
        border: 2px solid #e1e8ed;
        border-radius: 10px;
        padding: 15px 20px;
        font-size: 16px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .input-group-text {
        background: #667eea;
        border: 2px solid #667eea;
        color: white;
        border-radius: 10px 0 0 10px;
    }

    .input-group .form-control {
        border-left: none;
        border-radius: 0 10px 10px 0;
    }

    .btn-login {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 15px;
        font-size: 16px;
        font-weight: 600;
        color: white;
        width: 100%;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .alert {
        border-radius: 10px;
        border: none;
        margin-bottom: 20px;
    }

    .alert-danger {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
    }

    .login-footer {
        text-align: center;
        padding: 20px;
        background: rgba(0, 0, 0, 0.05);
        color: #666;
        font-size: 0.9rem;
    }

    .hospital-logo {
        width: 60px;
        height: 60px;
        background: white;
        border-radius: 50%;
        margin: 0 auto 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #2c3e50;
    }

    .captcha-container {
        background: rgba(248, 249, 250, 0.8);
        border-radius: 10px;
        padding: 15px;
        border: 2px solid #e1e8ed;
        margin-top: 5px;
    }

    .captcha-question {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        letter-spacing: 2px;
        margin-bottom: 10px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    #refresh-captcha {
        border-radius: 0 10px 10px 0;
        border-left: none;
        transition: all 0.3s ease;
    }

    #refresh-captcha:hover {
        background: #667eea;
        border-color: #667eea;
        color: white;
        transform: rotate(180deg);
    }

    .form-label {
        font-weight: 500;
        margin-bottom: 8px;
        color: #495057;
    }

    @media (max-width: 480px) {
        .login-container {
            margin: 20px;
            border-radius: 15px;
        }
        
        .login-header, .login-body {
            padding: 25px 20px;
        }
    }
</style>

<body>
    <div class="login-container">
        <div class="login-header">
            <div class="hospital-logo">
                <i class="fas fa-hospital"></i>
            </div>
            <h2>MONITORING CENDANA</h2>
            <p>Sistem Monitoring Gedung Cendana</p>
        </div>
        
        <div class="login-body">
            <?php
            $CI =& get_instance();
            if(isset($CI->session) && $CI->session->flashdata('error')): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= $CI->session->flashdata('error') ?>
                </div>
            <?php endif; ?>

            <?php if(isset($error_message)): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= $error_message ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="<?= function_exists('base_url') ? base_url('Auth/login') : 'Auth/login' ?>" id="loginForm">
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               name="username" 
                               id="username"
                               placeholder="Username" 
                               required 
                               autocomplete="username">
                    </div>
                </div>

                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password"
                               class="form-control"
                               name="password"
                               id="password"
                               placeholder="Password"
                               required
                               minlength="4"
                               autocomplete="current-password">
                    </div>
                </div>

                <div class="form-group">
                    <label for="captcha" class="form-label text-muted">Security Verification</label>
                    <div class="captcha-container">
                        <div class="captcha-question">
                            <span id="captcha-num1"></span> + <span id="captcha-num2"></span> = ?
                        </div>
                        <div class="input-group mt-2">
                            <span class="input-group-text">
                                <i class="fas fa-shield-alt"></i>
                            </span>
                            <input type="number"
                                   class="form-control"
                                   name="captcha_answer"
                                   id="captcha_answer"
                                   placeholder="Enter the result"
                                   required
                                   min="0"
                                   max="20">
                            <button type="button" class="btn btn-outline-secondary" id="refresh-captcha">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    <input type="hidden" name="captcha_result" id="captcha_result">
                </div>

                <div class="form-group">
                    <button type="submit" name="submit" class="btn btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Login
                    </button>
                </div>
            </form>
        </div>

        <div class="login-footer">
            <p>&copy; <?= date('Y') ?> RS PKN Cendana. All rights reserved.</p>
        </div>
    </div>

    <script src="assets/plugins/jquery/jquery-3.4.1.min.js"></script>
    <script src="assets/plugins/bootstrap/js/bootstrap.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Generate initial CAPTCHA
            generateCaptcha();

            // Focus on username field when page loads
            $('#username').focus();

            // CAPTCHA functions
            function generateCaptcha() {
                var num1 = Math.floor(Math.random() * 10) + 1;
                var num2 = Math.floor(Math.random() * 10) + 1;
                var result = num1 + num2;

                $('#captcha-num1').text(num1);
                $('#captcha-num2').text(num2);
                $('#captcha_result').val(result);
                $('#captcha_answer').val('');
            }

            // Refresh CAPTCHA button
            $('#refresh-captcha').on('click', function() {
                generateCaptcha();
                $(this).addClass('fa-spin');
                setTimeout(function() {
                    $('#refresh-captcha').removeClass('fa-spin');
                }, 500);
            });

            // Form validation
            $('#loginForm').on('submit', function(e) {
                var username = $('#username').val().trim();
                var password = $('#password').val();
                var captchaAnswer = parseInt($('#captcha_answer').val());
                var captchaResult = parseInt($('#captcha_result').val());

                if (username === '') {
                    e.preventDefault();
                    alert('Please enter your username');
                    $('#username').focus();
                    return false;
                }

                if (password === '') {
                    e.preventDefault();
                    alert('Please enter your password');
                    $('#password').focus();
                    return false;
                }

                if (password.length < 4) {
                    e.preventDefault();
                    alert('Password must be at least 4 characters long');
                    $('#password').focus();
                    return false;
                }

                if (isNaN(captchaAnswer) || captchaAnswer !== captchaResult) {
                    e.preventDefault();
                    alert('Please enter the correct CAPTCHA answer');
                    $('#captcha_answer').focus();
                    generateCaptcha(); // Generate new CAPTCHA on wrong answer
                    return false;
                }

                // Show loading state
                $('button[type="submit"]').html('<i class="fas fa-spinner fa-spin me-2"></i>Logging in...');
                $('button[type="submit"]').prop('disabled', true);
            });

            // Enter key handling
            $('#username, #password, #captcha_answer').on('keypress', function(e) {
                if (e.which === 13) { // Enter key
                    $('#loginForm').submit();
                }
            });

            // Auto-generate new CAPTCHA every 2 minutes for security
            setInterval(function() {
                generateCaptcha();
            }, 120000);
        });
    </script>
</body>
</html>
