
<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MonitoringPaCendana extends CI_Controller {

    function __construct()
    {
        parent::__construct();
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model('Model_pulang');
    }

public function index()
{
    // Load content within the dashboard template
    $data['page_content'] = 'monitoring_pa_cendana';
    $data['title'] = 'Monitoring PA Cendana';
    $this->load->view('template_dashboard', $data);
}

public function dashboard()
{
    // Load the monitoring content within the dashboard template
    $data['page_content'] = 'monitoring_pa_cendana';
    $data['title'] = 'Monitoring PA Cendana';
    $this->load->view('template_dashboard', $data);
}

public function content_only()
{
    // Load only the content without template (for AJAX or standalone use)
    $this->load->view('content_monitoring_pa_cendana');
}

public function get_histo_sito_data() {
    $tglAwal = $this->input->post('tgl_awal') ?? date('Y-m-d H:i:s');
    $tglAkhir = $this->input->post('tgl_akhir') ?? date('Y-m-d H:i:s');
    
    $listData = $this->Model_pulang->listHistoSito($tglAwal, $tglAkhir);
    
    $data = array();
    foreach ($listData->result() as $row) {
        $data[] = array(
            $row->NOPA,
            $row->NAMA_PASIEN,
            $row->NORM,
            $row->DSPA,
            $row->RUANG_ASAL,
            $row->MASUK_SAMPEL,
            $row->TANGGAL_HASIL,
            $row->LAMA_PEMERIKSAAN,
            $row->JENIZ,
            $row->FNL,
            $row->STATUS_LIS,
            $row->JENIS_PERIKSAAN
        );
    }
    
    $output = array(
        "draw" => intval($this->input->post("draw")),
        "recordsTotal" => $listData->num_rows(),
        "recordsFiltered" => $listData->num_rows(),
        "data" => $data
    );
    
    echo json_encode($output);
}

}



