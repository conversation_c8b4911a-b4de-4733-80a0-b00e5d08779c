<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <meta name="author" content="" />
    <title>User Management - Monitoring DPJP Praktek</title>
    <link href="https://fonts.googleapis.com/css?family=Poppins:400,500,700,800&display=swap" rel="stylesheet">
    <link href="assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/plugins/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="assets/plugins/perfectscroll/perfect-scrollbar.css" rel="stylesheet">
    <link href="assets/plugins/DataTables/datatables.min.css" rel="stylesheet">   
    <link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css" rel="stylesheet">
    
    <!-- Theme Styles -->
    <link href="assets/css/main.css" rel="stylesheet">
    <link href="assets/css/custom.css" rel="stylesheet">
</head>

<style>
    .user-card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
        margin-bottom: 20px;
    }
    
    .user-card:hover {
        transform: translateY(-2px);
    }

    .status-badge {
        font-size: 0.8em;
        padding: 0.3em 0.6em;
    }

    .action-buttons .btn {
        margin: 2px;
        padding: 5px 10px;
        font-size: 0.8em;
    }

    .modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
</style>

<body>
<div class="page-container">
    <div class="page-header">
        <nav class="navbar navbar-expand-lg d-flex justify-content-between">
            <div class="row" style="width:100%">
                <div class="col-md-4 col-sm-12 d-flex justify-content-start">
                    <h3 class="mt-3">USER MANAGEMENT</h3>
                </div>
                <div class="col-md-4 col-sm-12 d-flex justify-content-center">
                    <div id="tanggalku" style="font-size: 40px;"></div>
                </div>
                <div class="col-md-4 col-sm-12 d-flex justify-content-end pull-right">
                    <div class="row">
                        <div class="col-12">
                            <h4 style="text-align: end;"><?= date("l, d F Y") ?></h4>
                        </div>
                        <div class="col-12">
                            <h3 style="text-align: end;" class="total_users"><b>Total Users ()</b></h3>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </div>
    
    <div class="page-content" style="margin-top:0px">
        <div class="main-wrapper">
            <!-- Action Buttons -->
            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                <i class="fas fa-user-plus"></i> Add New User
                            </button>
                            <button type="button" class="btn btn-success" id="btnRefresh">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Users Table -->
            <div class="row">
                <div class="col">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="usersTable" class="display" style="width:100%">
                                    <thead>
                                        <tr style="text-align:center">
                                            <th>No</th>
                                            <th>ID</th>
                                            <th>Username</th>
                                            <th>Full Name</th>
                                            <th>Email</th>
                                            <th>Role</th>
                                            <th>Status</th>
                                            <th>Last Login</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data will be loaded via AJAX -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">
                    <i class="fas fa-user-plus me-2"></i>Add New User
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addUserForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required minlength="6">
                    </div>
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-control" id="role" name="role" required>
                            <option value="">Select Role</option>
                            <option value="admin">Administrator</option>
                            <option value="operator">Operator</option>
                            <option value="viewer">Viewer</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-control" id="status" name="status" required>
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Save User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">
                    <i class="fas fa-user-edit me-2"></i>Edit User
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editUserForm">
                <input type="hidden" id="edit_user_id" name="user_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="edit_username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_password" class="form-label">Password (leave blank to keep current)</label>
                        <input type="password" class="form-control" id="edit_password" name="password" minlength="6">
                    </div>
                    <div class="mb-3">
                        <label for="edit_full_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="edit_full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="edit_role" class="form-label">Role</label>
                        <select class="form-control" id="edit_role" name="role" required>
                            <option value="">Select Role</option>
                            <option value="admin">Administrator</option>
                            <option value="operator">Operator</option>
                            <option value="viewer">Viewer</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status</label>
                        <select class="form-control" id="edit_status" name="status" required>
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Update User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="assets/plugins/jquery/jquery-3.4.1.min.js"></script>
<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="assets/plugins/bootstrap/js/bootstrap.min.js"></script>
<script src="https://unpkg.com/feather-icons"></script>
<script src="assets/plugins/perfectscroll/perfect-scrollbar.min.js"></script>
<script src="assets/plugins/DataTables/datatables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="assets/js/main.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#usersTable').DataTable({
        responsive: true,
        processing: true,
        serverSide: false,
        ajax: {
            url: 'UserManagement/get_users_data', // This would need to be created
            type: 'POST',
            dataSrc: function(json) {
                if (json.recordsTotal !== undefined) {
                    $(".total_users").text('Total Users (' + json.recordsTotal + ')');
                }
                return json.data || [];
            }
        },
        columnDefs: [
            {
                targets: [0, 1, 6, 8], // Center align certain columns
                className: 'text-center'
            },
            {
                targets: [6], // Status column
                render: function(data, type, row) {
                    var badgeClass = data == '1' ? 'bg-success' : 'bg-danger';
                    var statusText = data == '1' ? 'Active' : 'Inactive';
                    return '<span class="badge ' + badgeClass + ' status-badge">' + statusText + '</span>';
                }
            },
            {
                targets: [8], // Actions column
                render: function(data, type, row) {
                    return '<div class="action-buttons">' +
                           '<button class="btn btn-sm btn-warning edit-user" data-id="' + row[1] + '">' +
                           '<i class="fas fa-edit"></i> Edit</button>' +
                           '<button class="btn btn-sm btn-danger delete-user" data-id="' + row[1] + '">' +
                           '<i class="fas fa-trash"></i> Delete</button>' +
                           '</div>';
                }
            }
        ],
        order: [[0, 'asc']]
    });

    // Refresh button
    $('#btnRefresh').on('click', function() {
        table.ajax.reload();
    });

    // Add user form submission
    $('#addUserForm').on('submit', function(e) {
        e.preventDefault();
        // Add AJAX call to save user
        alert('Add user functionality would be implemented here');
    });

    // Edit user form submission
    $('#editUserForm').on('submit', function(e) {
        e.preventDefault();
        // Add AJAX call to update user
        alert('Edit user functionality would be implemented here');
    });

    // Edit user button click
    $(document).on('click', '.edit-user', function() {
        var userId = $(this).data('id');
        // Load user data and show edit modal
        $('#editUserModal').modal('show');
    });

    // Delete user button click
    $(document).on('click', '.delete-user', function() {
        var userId = $(this).data('id');
        if (confirm('Are you sure you want to delete this user?')) {
            // Add AJAX call to delete user
            alert('Delete user functionality would be implemented here');
        }
    });
});

// Clock function
window.setTimeout("waktu()", 1000);
function waktu() {
    var tanggal = new Date();
    setTimeout("waktu()", 1000);
    document.getElementById("tanggalku").innerHTML = tanggal.getHours() + " : " + tanggal.getMinutes() + " : " + tanggal.getSeconds();
}
</script>
</body>
</html>
