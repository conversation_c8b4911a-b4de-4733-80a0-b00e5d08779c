<style>
    .hijau {
        background-color: #10ac84;
        color: white;
    }

    .kuning{
        background: #feb019d9;
        color: white;
    }

    .merah {
        background-color: #c9001dd9;
        color: white;
    }

    .orange {
        background-color: #ff6d00;
        color: white;
    }

    .abu_abu {
        background-color: #33b2ff;
        color: white;
    }

    .cream {
        background-color: #9f9ca5;
        color: white;
    }

    .hitam {
        background-color: #000000;
        color: white;
    }

    /* DataTables Buttons Styling */
    .dt-buttons {
        margin-bottom: 15px;
    }

    .dt-button {
        margin-right: 5px !important;
        border-radius: 4px !important;
        font-size: 12px !important;
        padding: 6px 12px !important;
    }

    .dt-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
</style>

<!-- Page Header -->
<div class="page-header">
    <div class="row" style="width:100%">
        <div class="col-md-4 col-sm-12 d-flex justify-content-start">
            <h3 class="mt-3">MONITORING HASIL LAB PA GEDUNG CENDANA</h3>
        </div>
        <div class="col-md-4 col-sm-12 d-flex justify-content-center">
            <div id="tanggalku" style="font-size: 40px;"></div>
        </div>
        <div class="col-md-4 col-sm-12 d-flex justify-content-end pull-right">
            <div class="row">
                <div class="col-12">
                    <h4 style="text-align: end;"><?= date("l, d F Y") ?></h4>
                </div>
                <div class="col-12">
                    <h3 style="text-align: end;" class="total_pasien"><b>Total Pemeriksaan ()</b></h3>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-12 mb-3">
            <div class="card">
                <div class="card-body">
                    <form id="filterForm" class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Tanggal Awal</label>
                                <input type="datetime-local" class="form-control" id="tgl_awal" name="tgl_awal" value="<?= date('Y-m-d 00:00:00') ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Tanggal Akhir</label>
                                <input type="datetime-local" class="form-control" id="tgl_akhir" name="tgl_akhir" value="<?= date('Y-m-d 23:59:59') ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="button" id="btnFilter" class="btn btn-primary form-control">Filter</button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Filter Status</label>
                                <select class="form-control" id="status_filter" name="status_filter">
                                    <option value="">Semua Status</option>
                                    <option value="hijau" style="background-color: #10ac84; color: white;">🟢 Hijau (Final + Tepat Waktu)</option>
                                    <option value="kuning" style="background-color: #feb019d9; color: white;">🟡 Kuning (Final + Perhatian)</option>
                                    <option value="merah" style="background-color: #c9001dd9; color: white;">🔴 Merah (Final + Terlambat)</option>
                                    <option value="abu_abu" style="background-color: #33b2ff; color: white;">🔵 Biru (Belum Final + Tepat Waktu)</option>
                                    <option value="cream" style="background-color: #9f9ca5; color: white;">⚫ Abu-abu (Belum Final + Perhatian)</option>
                                    <option value="hitam" style="background-color: #000000; color: white;">⚫ Hitam (Belum Final + Terlambat)</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="myTable" class="display" style="width:100%">
                            <thead>
                                <tr style="text-align:center">
                                    <th>No. Lab</th>
                                    <th>Nama Pasien</th>
                                    <th>No. RM</th>
                                    <th>DOKTER</th>
                                    <th>Ruang Asal</th>
                                    <th>Masuk Sampel</th>
                                    <th>Tanggal Hasil</th>
                                    <th style="text-align: center;">Lama Pemeriksaan</th>
                                    <th>Jenis</th>
                                    <th>Status</th>
                                    <th>Status LIS</th>
                                    <th>JENIS PEMERIKSAAN</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Wait for template scripts to load
function initMonitoringTable() {
    // Check if jQuery and DataTables are available
    if (typeof jQuery === 'undefined') {
        console.log('jQuery not loaded yet, retrying...');
        setTimeout(initMonitoringTable, 100);
        return;
    }

    if (typeof jQuery.fn.DataTable === 'undefined') {
        console.log('DataTables not loaded yet, retrying...');
        setTimeout(initMonitoringTable, 100);
        return;
    }

    $(document).ready(function() {
    var table = $('#myTable').DataTable({
        responsive: true,
        processing: true,
        serverSide: false,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'pdfHtml5',
                text: '<i class="fas fa-file-pdf"></i> Export PDF',
                className: 'btn btn-danger btn-sm',
                title: 'Monitoring PA Cendana - ' + new Date().toLocaleDateString('id-ID'),
                orientation: 'landscape',
                pageSize: 'A4',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11] // Exclude Status LIS column (index 10)
                },
                customize: function(doc) {
                    doc.defaultStyle.fontSize = 8;
                    doc.styles.tableHeader.fontSize = 9;
                    doc.styles.tableHeader.fillColor = '#2c3e50';
                    doc.content[1].table.widths = ['8%', '15%', '8%', '12%', '10%', '12%', '12%', '8%', '8%', '7%', '10%'];
                }
            },
            {
                extend: 'excelHtml5',
                text: '<i class="fas fa-file-excel"></i> Export Excel',
                className: 'btn btn-success btn-sm',
                title: 'Monitoring PA Cendana - ' + new Date().toLocaleDateString('id-ID'),
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11] // Exclude Status LIS column (index 10)
                }
            }
        ],
        ajax: {
            url: 'MonitoringPaCendana/get_monitoring_data',
            type: 'POST',
            data: function(d) {
                d.tgl_awal = $('#tgl_awal').val();
                d.tgl_akhir = $('#tgl_akhir').val();
                d.status_filter = $('#status_filter').val();
            },
            dataSrc: function(json) {
                if (json.recordsTotal !== undefined) {
                    $(".total_pasien").text('Total Pemeriksaan (' + json.recordsTotal + ')');
                }
                return json.data || [];
            }
        },
        columnDefs: [
            {
                targets: [0, 2, 7, 8, 9, 10], // Center align certain columns
                className: 'text-center'
            },
            {
                targets: [9], // Status column
                render: function(data, type, row) {
                    return '<span class="' + data + '">' + data.toUpperCase() + '</span>';
                }
            }
        ],
        order: [[0, 'desc']], // Order by No. Lab descending
        pageLength: 100,
        lengthMenu: [25, 50, 100, 200]
    });

    $('#btnFilter').on('click', function() {
        table.ajax.reload();
    });

    $('#status_filter').on('change', function() {
        table.ajax.reload();
    });

    function reloadDatatable() {
        $('#myTable').DataTable().ajax.reload(null, false);
    }
    
    setInterval(reloadDatatable, 300000); // Refresh every 5 minutes

    // Clock function
    window.setTimeout("waktu()", 1000);
    function waktu() {
        var tanggal = new Date();
        setTimeout("waktu()", 1000);
        if (document.getElementById("tanggalku")) {
            document.getElementById("tanggalku").innerHTML = tanggal.getHours() + " : " + tanggal.getMinutes() + " : " + tanggal.getSeconds();
        }
    }

    }); // End document ready
}

// Initialize the monitoring table
initMonitoringTable();
</script>
