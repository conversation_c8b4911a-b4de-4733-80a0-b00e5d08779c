<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON>jian extends CI_Controller {

    function __construct()
    {
        parent::__construct();
        date_default_timezone_set("Asia/Bangkok");
        $this->load->helper('url');
        $this->load->library('session');
        $this->load->model('Model_pulang');
    }

    public function index() {
        // Load content within the dashboard template
        $data['page_content'] = 'perjanjian';
        $data['title'] = 'Data Perjanjian';
        $this->load->view('template_dashboard', $data);
    }

    public function dashboard() {
        // Load the perjanjian content within the dashboard template
        $data['page_content'] = 'perjanjian';
        $data['title'] = 'Data Perjanjian';
        $this->load->view('template_dashboard', $data);
    }

    public function content_only() {
        // Load only the content without template (for AJAX or standalone use)
        $this->load->view('content_perjanjian');
    }

    public function test_connection() {
        echo "Controller is working! Current time: " . date('Y-m-d H:i:s');
    }

    public function get_perjanjian_data() {
        try {
            $tglAwal = $this->input->post('tgl_awal') ?? date('Y-m-d 00:00:00');
            $tglAkhir = $this->input->post('tgl_akhir') ?? date('Y-m-d 23:59:59');

            // Debug: Log the parameters
            log_message('debug', 'Perjanjian - tglAwal: ' . $tglAwal . ', tglAkhir: ' . $tglAkhir);

            $listData = $this->Model_pulang->perjanjian($tglAwal, $tglAkhir);

            if (!$listData) {
                throw new Exception('Query failed or returned null');
            }

            $data = array();
            $totalRows = $listData->num_rows();

            // Debug: Log the number of rows
            log_message('debug', 'Perjanjian - Total rows: ' . $totalRows);

            if ($totalRows > 0) {
                $nomor = 1;
                foreach ($listData->result() as $row) {
                    $data[] = array(
                        $nomor++,
                        $row->NOMR ?? '',
                        $row->NAMAPASIEN ?? '',
                        $row->NOMOR ?? '',
                        $row->DOKTER ?? '',
                        $row->RUANGANDESK ?? '',
                        $row->OLEH ?? '',
                        $row->PENJAMIN ?? '',
                        $row->KET ?? ''
                    );
                }
            }

            $output = array(
                "draw" => intval($this->input->post("draw")),
                "recordsTotal" => $totalRows,
                "recordsFiltered" => $totalRows,
                "data" => $data
            );

            echo json_encode($output);

        } catch (Exception $e) {
            log_message('error', 'Perjanjian Error: ' . $e->getMessage());

            $output = array(
                "draw" => intval($this->input->post("draw")),
                "recordsTotal" => 0,
                "recordsFiltered" => 0,
                "data" => array(),
                "error" => $e->getMessage()
            );

            echo json_encode($output);
        }
    }

    public function get_summary_data() {
        try {
            $tglAwal = $this->input->post('tgl_awal') ?? date('Y-m-d 00:00:00');
            $tglAkhir = $this->input->post('tgl_akhir') ?? date('Y-m-d 23:59:59');

            $listData = $this->Model_pulang->perjanjian($tglAwal, $tglAkhir);

            $summary = array(
                'total_perjanjian' => 0,
                'total_dokter' => 0,
                'total_ruangan' => 0,
                'total_pagi' => 0,
                'total_sore' => 0,
                'total_malam' => 0,
                'total_dengan_jadwal' => 0
            );

            if ($listData && $listData->num_rows() > 0) {
                $summary['total_perjanjian'] = $listData->num_rows();
                $dokterList = array();
                $ruanganList = array();

                foreach ($listData->result() as $row) {
                    // Count unique doctors
                    $dokter = $row->DOKTER ?? '';
                    if (!empty($dokter) && !in_array($dokter, $dokterList)) {
                        $dokterList[] = $dokter;
                    }

                    // Count unique rooms
                    $ruangan = $row->RUANGANDESK ?? '';
                    if (!empty($ruangan) && !in_array($ruangan, $ruanganList)) {
                        $ruanganList[] = $ruangan;
                    }

                    // Count by time slots (KET contains time ranges like [08:00 - 09:00])
                    $ket = $row->KET ?? '';
                    if (!empty($ket) && strpos($ket, '[') !== false) {
                        $summary['total_dengan_jadwal']++;

                        // Extract hour from time slot
                        if (preg_match('/\[(\d{2}):/', $ket, $matches)) {
                            $hour = intval($matches[1]);
                            if ($hour >= 6 && $hour < 12) {
                                $summary['total_pagi']++;
                            } elseif ($hour >= 12 && $hour < 18) {
                                $summary['total_sore']++;
                            } else {
                                $summary['total_malam']++;
                            }
                        }
                    }
                }

                $summary['total_dokter'] = count($dokterList);
                $summary['total_ruangan'] = count($ruanganList);
            }

            echo json_encode($summary);

        } catch (Exception $e) {
            log_message('error', 'Perjanjian Summary Error: ' . $e->getMessage());

            $summary = array(
                'total_perjanjian' => 0,
                'total_baru' => 0,
                'total_lama' => 0,
                'total_poli_pagi' => 0,
                'total_poli_sore' => 0,
                'total_berkunjung' => 0,
                'total_telemedicine' => 0,
                'error' => $e->getMessage()
            );

            echo json_encode($summary);
        }
    }

    public function export_excel() {
        // Excel export functionality - to be implemented
        echo "Excel export functionality - coming soon";
    }
}
