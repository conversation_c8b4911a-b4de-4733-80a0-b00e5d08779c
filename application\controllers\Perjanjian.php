<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON><PERSON><PERSON> extends CI_Controller {

    function __construct()
    {
        parent::__construct();
        date_default_timezone_set("Asia/Bangkok");
        $this->load->helper('url');
        $this->load->model('Model_pulang');
    }

    public function index() {
        $this->load->view('v_perjanjian');
    }

    public function test_connection() {
        echo "Controller is working! Current time: " . date('Y-m-d H:i:s');
    }

    public function get_perjanjian_data() {
        try {
            $tglAwal = $this->input->post('tgl_awal') ?? date('Y-m-d 00:00:00');
            $tglAkhir = $this->input->post('tgl_akhir') ?? date('Y-m-d 23:59:59');

            // Debug: Log the parameters
            log_message('debug', 'Perjanjian - tglAwal: ' . $tglAwal . ', tglAkhir: ' . $tglAkhir);

            $listData = $this->Model_pulang->perjanjian($tglAwal, $tglAkhir);

            if (!$listData) {
                throw new Exception('Query failed or returned null');
            }

            $data = array();
            $totalRows = $listData->num_rows();

            // Debug: Log the number of rows
            log_message('debug', 'Perjanjian - Total rows: ' . $totalRows);

            if ($totalRows > 0) {
                $nomor = 1;
                foreach ($listData->result() as $row) {
                    $data[] = array(
                        $nomor++,
                        $row->NOMR ?? '',
                        $row->NAMAPASIEN ?? '',
                        $row->status ?? '',
                        $row->RENCANA ?? '',
                        $row->NOMOR ?? '',
                        $row->OLEH ?? '',
                        $row->PENJAMIN ?? '',
                        $row->STATUS_PASIEN ?? '',
                        $row->KET ?? ''
                    );
                }
            }

            $output = array(
                "draw" => intval($this->input->post("draw")),
                "recordsTotal" => $totalRows,
                "recordsFiltered" => $totalRows,
                "data" => $data
            );

            echo json_encode($output);

        } catch (Exception $e) {
            log_message('error', 'Perjanjian Error: ' . $e->getMessage());

            $output = array(
                "draw" => intval($this->input->post("draw")),
                "recordsTotal" => 0,
                "recordsFiltered" => 0,
                "data" => array(),
                "error" => $e->getMessage()
            );

            echo json_encode($output);
        }
    }

    public function get_summary_data() {
        try {
            $tglAwal = $this->input->post('tgl_awal') ?? date('Y-m-d 00:00:00');
            $tglAkhir = $this->input->post('tgl_akhir') ?? date('Y-m-d 23:59:59');

            $listData = $this->Model_pulang->perjanjian($tglAwal, $tglAkhir);

            $summary = array(
                'total_perjanjian' => 0,
                'total_baru' => 0,
                'total_lama' => 0,
                'total_poli_pagi' => 0,
                'total_poli_sore' => 0,
                'total_berkunjung' => 0,
                'total_telemedicine' => 0
            );

            if ($listData && $listData->num_rows() > 0) {
                $summary['total_perjanjian'] = $listData->num_rows();
                
                foreach ($listData->result() as $row) {
                    // Count by status (Baru/Lama)
                    if (($row->status ?? '') == 'Baru') {
                        $summary['total_baru']++;
                    } elseif (($row->status ?? '') == 'Lama') {
                        $summary['total_lama']++;
                    }
                    
                    // Count by schedule type
                    if (($row->KET ?? '') == 'Poli Pagi') {
                        $summary['total_poli_pagi']++;
                    } elseif (($row->KET ?? '') == 'Poli Sore') {
                        $summary['total_poli_sore']++;
                    }
                    
                    // Count by visit type
                    $rencana = $row->RENCANA ?? '';
                    if (strpos($rencana, 'Berkunjung Ke RS') !== false) {
                        $summary['total_berkunjung']++;
                    } elseif (strpos($rencana, 'Telemedicine') !== false) {
                        $summary['total_telemedicine']++;
                    }
                }
            }

            echo json_encode($summary);

        } catch (Exception $e) {
            log_message('error', 'Perjanjian Summary Error: ' . $e->getMessage());

            $summary = array(
                'total_perjanjian' => 0,
                'total_baru' => 0,
                'total_lama' => 0,
                'total_poli_pagi' => 0,
                'total_poli_sore' => 0,
                'total_berkunjung' => 0,
                'total_telemedicine' => 0,
                'error' => $e->getMessage()
            );

            echo json_encode($summary);
        }
    }

    public function export_excel() {
        // Excel export functionality - to be implemented
        echo "Excel export functionality - coming soon";
    }
}
