<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Auth extends CI_Controller{

    function __construct() {
        parent::__construct();
        $this->load->model('model_operator');
        $this->load->library('session');
        $this->load->helper('url');
    }

    public function login(){
        if($_SERVER['REQUEST_METHOD'] == 'POST' && $this->input->post()){
            // proses login disini
            $username   =   $this->input->post('username');
            $password   =   $this->input->post('password');
            $captcha_answer = $this->input->post('captcha_answer');
            $captcha_result = $this->input->post('captcha_result');
            if (empty($username) || empty($password)) {
                $this->session->set_flashdata('error', 'Username and password are required.');
                redirect('Auth/login');
                return;
            }
            if (strlen($password) < 4) {
                $this->session->set_flashdata('error', 'Password must be at least 4 characters long.');
                redirect('Auth/login');
                return;
            }

            // CAPTCHA validation
            if (empty($captcha_answer) || empty($captcha_result)) {
                $this->session->set_flashdata('error', 'Please complete the security verification.');
                redirect('Auth/login');
                return;
            }

            if ((int)$captcha_answer !== (int)$captcha_result) {
                $this->session->set_flashdata('error', 'Security verification failed. Please try again.');
                redirect('Auth/login');
                return;
            }

            $data=  $this->model_operator->log_in($username,$password);
           // print_r($data);
            if ($data == '' || $data == null) {
                $this->session->set_flashdata('error', 'Invalid username or password.');
                redirect('Auth/login');
              } else {
                    $private_key = 'KDFLDMSTHBWWSGCBH';
                    $hashed_password = $data->PASSWORD;
                    $id = $data->ID;
                    $username = $data->LOGIN;
                    $nama = $data->NAMA;
                    $passwordMD5 = MD5($private_key . MD5($password) . $private_key);
            
                    if (hash_equals($hashed_password, $passwordMD5) || $data->PASSWORD == $data->PASS) {
                        $this->session->set_userdata(array('status_login'=>'oke','username'=>$username));
                        $session = array(
                        'id' => $id,
                        'username' => $username,
                        'nama' => $nama,
                        'status_login' => 'oke',
                        );
                        $this->session->set_userdata($session);
                          $this->load->view('template_dashboard');
                    } else {
                        $this->session->set_flashdata('error', 'Invalid username or password.');
                        redirect('Auth/login');
                    }
                    
            }
        }else{
            $this->load->view('form_login');
        }
    }


    
    
    function logout()
    {
        $this->session->sess_destroy();
        redirect('');
    }
}