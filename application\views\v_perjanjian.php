<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <meta name="author" content="" />
    <title>Data Per<PERSON></title>
    <link href="https://fonts.googleapis.com/css?family=Poppins:400,500,700,800&display=swap" rel="stylesheet">
    <link href="assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/plugins/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="assets/plugins/perfectscroll/perfect-scrollbar.css" rel="stylesheet">
    <link href="assets/plugins/DataTables/datatables.min.css" rel="stylesheet">   
    <link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css" rel="stylesheet">
    
    <!-- Theme Styles -->
    <link href="assets/css/main.css" rel="stylesheet">
    <link href="assets/css/custom.css" rel="stylesheet">
</head>

<style>
    /* DataTables Buttons Styling */
    .dt-buttons {
        margin-bottom: 15px;
    }
    
    .dt-button {
        margin-right: 5px !important;
        border-radius: 4px !important;
        font-size: 12px !important;
        padding: 6px 12px !important;
    }
    
    .dt-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    .summary-card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    
    .summary-card:hover {
        transform: translateY(-2px);
    }

    .status-badge {
        font-size: 0.8em;
        padding: 0.3em 0.6em;
    }
</style>

<body>
<div class="page-container">
    <div class="page-header">
        <nav class="navbar navbar-expand-lg d-flex justify-content-between">
            <div class="row" style="width:100%">
                <div class="col-md-4 col-sm-12 d-flex justify-content-start">
                    <h3 class="mt-3">MONITORING - DATA PERJANJIAN</h3>
                </div>
                <div class="col-md-4 col-sm-12 d-flex justify-content-center">
                    <div id="tanggalku" style="font-size: 40px;"></div>
                </div>
                <div class="col-md-4 col-sm-12 d-flex justify-content-end pull-right">
                    <div class="row">
                        <div class="col-12">
                            <h4 style="text-align: end;"><?= date("l, d F Y") ?></h4>
                        </div>
                        <div class="col-12">
                            <h3 style="text-align: end;" class="total_perjanjian"><b>Total Perjanjian ()</b></h3>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </div>
    
    <div class="page-content" style="margin-top:0px">
        <div class="main-wrapper">
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card summary-card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">Total Perjanjian</h4>
                            <h2 id="total_perjanjian_count">0</h2>
                            <small><i class="fas fa-calendar-check"></i> Total Appointment</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card summary-card bg-success text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">Pasien Baru</h4>
                            <h2 id="total_baru_count">0</h2>
                            <small><i class="fas fa-user-plus"></i> New Patients</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card summary-card bg-info text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">Pasien Lama</h4>
                            <h2 id="total_lama_count">0</h2>
                            <small><i class="fas fa-user-check"></i> Returning Patients</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card summary-card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">Poli Pagi</h4>
                            <h2 id="total_poli_pagi_count">0</h2>
                            <small><i class="fas fa-sun"></i> Morning Schedule</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Summary Row -->
            <div class="row mb-3">
                <div class="col-md-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">Poli Sore</h4>
                            <h2 id="total_poli_sore_count">0</h2>
                            <small><i class="fas fa-moon"></i> Evening Schedule</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">Berkunjung</h4>
                            <h2 id="total_berkunjung_count">0</h2>
                            <small><i class="fas fa-hospital"></i> Hospital Visit</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-dark text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">Telemedicine</h4>
                            <h2 id="total_telemedicine_count">0</h2>
                            <small><i class="fas fa-video"></i> Online Consultation</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filter Section -->
            <div class="row">
                <div class="col-md-12 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <form id="filterForm" class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Tanggal Awal</label>
                                        <input type="datetime-local" class="form-control" id="tgl_awal" name="tgl_awal" value="<?= date('Y-m-d\T00:00') ?>">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Tanggal Akhir</label>
                                        <input type="datetime-local" class="form-control" id="tgl_akhir" name="tgl_akhir" value="<?= date('Y-m-d\T23:59') ?>">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <button type="button" id="btnFilter" class="btn btn-primary form-control">
                                            <i class="fas fa-search"></i> Filter Data
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Data Table -->
            <div class="row">
                <div class="col">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="perjanjianTable" class="display" style="width:100%">
                                    <thead>
                                        <tr style="text-align:center">
                                            <th>No</th>
                                            <th>No. RM</th>
                                            <th>Nama Pasien</th>
                                            <th>Status</th>
                                            <th>Rencana</th>
                                            <th>No. Perjanjian</th>
                                            <th>Dibuat Oleh</th>
                                            <th>Penjamin</th>
                                            <th>Status Pasien</th>
                                            <th>Keterangan</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="assets/plugins/jquery/jquery-3.4.1.min.js"></script>
<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="assets/plugins/bootstrap/js/bootstrap.min.js"></script>
<script src="https://unpkg.com/feather-icons"></script>
<script src="assets/plugins/perfectscroll/perfect-scrollbar.min.js"></script>
<script src="assets/plugins/DataTables/datatables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="assets/js/main.min.js"></script>

<script>
$(document).ready(function() {
    console.log('Document ready, initializing Perjanjian...');

    // Get base URL dynamically
    var currentPath = window.location.pathname;
    var baseUrl = '';

    if (currentPath.includes('index.php')) {
        baseUrl = window.location.origin + currentPath.split('/index.php')[0] + '/index.php/';
    } else {
        baseUrl = window.location.origin + '/monitoring_cendana/';
    }
    console.log('Base URL:', baseUrl);

    var table = $('#perjanjianTable').DataTable({
        responsive: true,
        processing: true,
        serverSide: false,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'pdfHtml5',
                text: '<i class="fas fa-file-pdf"></i> Export PDF',
                className: 'btn btn-danger btn-sm',
                title: function() {
                    var tglAwal = $('#tgl_awal').val();
                    var tglAkhir = $('#tgl_akhir').val();
                    var periode = '';

                    if (tglAwal && tglAkhir) {
                        var startDate = new Date(tglAwal).toLocaleDateString('id-ID');
                        var endDate = new Date(tglAkhir).toLocaleDateString('id-ID');
                        periode = 'Periode: ' + startDate + ' - ' + endDate;
                    } else {
                        periode = 'Periode: ' + new Date().toLocaleDateString('id-ID');
                    }

                    return 'Data Perjanjian - ' + periode;
                },
                orientation: 'landscape',
                pageSize: 'A4',
                exportOptions: {
                    columns: ':visible'
                },
                customize: function(doc) {
                    doc.defaultStyle.fontSize = 8;
                    doc.styles.tableHeader.fontSize = 9;
                    doc.styles.tableHeader.fillColor = '#2c3e50';
                }
            },
            {
                extend: 'excelHtml5',
                text: '<i class="fas fa-file-excel"></i> Export Excel',
                className: 'btn btn-success btn-sm',
                title: function() {
                    var tglAwal = $('#tgl_awal').val();
                    var tglAkhir = $('#tgl_akhir').val();
                    var periode = '';

                    if (tglAwal && tglAkhir) {
                        var startDate = new Date(tglAwal).toLocaleDateString('id-ID');
                        var endDate = new Date(tglAkhir).toLocaleDateString('id-ID');
                        periode = 'Periode: ' + startDate + ' - ' + endDate;
                    } else {
                        periode = 'Periode: ' + new Date().toLocaleDateString('id-ID');
                    }

                    return 'Data Perjanjian - ' + periode;
                },
                exportOptions: {
                    columns: ':visible'
                }
            }
        ],
        ajax: {
            url: baseUrl + 'Perjanjian/get_perjanjian_data',
            type: 'POST',
            data: function(d) {
                d.tgl_awal = $('#tgl_awal').val();
                d.tgl_akhir = $('#tgl_akhir').val();
                console.log('Sending data:', d);
            },
            dataSrc: function(json) {
                console.log('DataTable response:', json);
                if (json.error) {
                    console.error('Server error:', json.error);
                    alert('Error: ' + json.error);
                    return [];
                }

                // Update total count
                if (json.recordsTotal !== undefined) {
                    $(".total_perjanjian").text('Total Perjanjian (' + json.recordsTotal + ')');
                    loadSummaryData();
                }

                return json.data || [];
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                console.error('Response:', xhr.responseText);
                alert('AJAX Error: ' + error + '\nCheck console for details');
            }
        },
        columnDefs: [
            {
                targets: [0, 5], // Center align numeric columns
                className: 'text-center'
            },
            {
                targets: [3], // Status column
                render: function(data, type, row) {
                    var badgeClass = data === 'Baru' ? 'bg-success' : 'bg-info';
                    return '<span class="badge ' + badgeClass + ' status-badge">' + data + '</span>';
                },
                className: 'text-center'
            },
            {
                targets: [8], // Status Pasien column
                render: function(data, type, row) {
                    var badgeClass = 'bg-secondary';
                    if (data === 'Hidup') {
                        badgeClass = 'bg-success';
                    } else if (data === 'Meninggal') {
                        badgeClass = 'bg-danger';
                    } else if (data === 'Non Aktif') {
                        badgeClass = 'bg-warning';
                    }
                    return '<span class="badge ' + badgeClass + ' status-badge">' + data + '</span>';
                },
                className: 'text-center'
            },
            {
                targets: [9], // Keterangan column
                render: function(data, type, row) {
                    var badgeClass = data === 'Poli Pagi' ? 'bg-warning' : 'bg-secondary';
                    return '<span class="badge ' + badgeClass + ' status-badge">' + data + '</span>';
                },
                className: 'text-center'
            }
        ],
        ordering: true,
        searching: true,
        lengthChange: true,
        pageLength: 100,
        lengthMenu: [25, 50, 100, 200],
        order: [[0, 'asc']], // Order by number ascending
        initComplete: function(settings, json) {
            console.log('DataTable initialization complete');
        },
        drawCallback: function(settings) {
            console.log('DataTable draw callback triggered');
        }
    });

    $('#btnFilter').on('click', function() {
        console.log('Filter button clicked');
        table.ajax.reload();
    });

    function loadSummaryData() {
        console.log('Loading summary data...');
        var currentPath = window.location.pathname;
        var baseUrl = '';

        if (currentPath.includes('index.php')) {
            baseUrl = window.location.origin + currentPath.split('/index.php')[0] + '/index.php/';
        } else {
            baseUrl = window.location.origin + '/monitoring_cendana/';
        }
        $.ajax({
            url: baseUrl + 'Perjanjian/get_summary_data',
            type: 'POST',
            data: {
                tgl_awal: $('#tgl_awal').val(),
                tgl_akhir: $('#tgl_akhir').val()
            },
            success: function(response) {
                console.log('Summary response:', response);
                try {
                    var data = JSON.parse(response);
                    $('#total_perjanjian_count').text(data.total_perjanjian || 0);
                    $('#total_baru_count').text(data.total_baru || 0);
                    $('#total_lama_count').text(data.total_lama || 0);
                    $('#total_poli_pagi_count').text(data.total_poli_pagi || 0);
                    $('#total_poli_sore_count').text(data.total_poli_sore || 0);
                    $('#total_berkunjung_count').text(data.total_berkunjung || 0);
                    $('#total_telemedicine_count').text(data.total_telemedicine || 0);
                } catch (e) {
                    console.error('Error parsing summary response:', e);
                }
            },
            error: function(xhr, status, error) {
                console.error('Summary AJAX Error:', status, error);
            }
        });
    }

    function reloadDatatable() {
        $('#perjanjianTable').DataTable().ajax.reload(null, false);
    }

    setInterval(reloadDatatable, 300000); // Refresh every 5 minutes
});
</script>

<script type="text/javascript">
    window.setTimeout("waktu()", 1000);
    function waktu() {
        var tanggal = new Date();
        setTimeout("waktu()", 1000);
        document.getElementById("tanggalku").innerHTML = tanggal.getHours() + " : " + tanggal.getMinutes() + " : " + tanggal.getSeconds();
    }
</script>
</body>
</html>
