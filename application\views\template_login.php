<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="Login - Monitoring DPJP Praktek" />
    <meta name="author" content="" />
    <title>Login - Monitoring DPJP Praktek</title>
    <link href="https://fonts.googleapis.com/css?family=Poppins:400,500,700,800&display=swap" rel="stylesheet">
    <link href="<?= base_url('assets/plugins/bootstrap/css/bootstrap.min.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/plugins/font-awesome/css/all.min.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/css/main.css') ?>" rel="stylesheet">
</head>

<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Poppins', sans-serif;
        margin: 0;
        padding: 20px;
    }

    .login-wrapper {
        width: 100%;
        max-width: 450px;
        margin: 0 auto;
    }

    .login-container {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        animation: slideUp 0.6s ease-out;
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .login-header {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: white;
        padding: 40px 30px;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .login-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: pulse 4s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); opacity: 0.5; }
        50% { transform: scale(1.1); opacity: 0.8; }
    }

    .hospital-logo {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 50%;
        margin: 0 auto 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        color: white;
        border: 3px solid rgba(255, 255, 255, 0.3);
        position: relative;
        z-index: 1;
    }

    .login-header h2 {
        margin: 0 0 10px 0;
        font-weight: 700;
        font-size: 2rem;
        position: relative;
        z-index: 1;
    }

    .login-header p {
        margin: 0;
        opacity: 0.9;
        font-size: 1rem;
        position: relative;
        z-index: 1;
    }

    .content-wrapper {
        padding: 0;
    }

    /* Responsive Design */
    @media (max-width: 480px) {
        body {
            padding: 10px;
        }
        
        .login-container {
            border-radius: 15px;
        }
        
        .login-header {
            padding: 30px 20px;
        }
        
        .hospital-logo {
            width: 60px;
            height: 60px;
            font-size: 24px;
        }
        
        .login-header h2 {
            font-size: 1.5rem;
        }
        
        .login-header p {
            font-size: 0.9rem;
        }
    }

    /* Loading Animation */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid rgba(255, 255, 255, 0.3);
        border-top: 5px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<body>
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <div class="login-wrapper">
        <div class="login-container">
            <div class="login-header">
                <div class="hospital-logo">
                    <i class="fas fa-hospital"></i>
                </div>
                <h2>MONITORING DPJP</h2>
                <p>Sistem Monitoring DPJP Praktek</p>
            </div>
            
            <div class="content-wrapper">
                <?php 
                if(isset($contents)) {
                    echo $contents;
                } else {
                    $this->load->view('form_login');
                }
                ?>
            </div>
        </div>
    </div>

    <script src="<?= base_url('assets/plugins/jquery/jquery-3.4.1.min.js') ?>"></script>
    <script src="<?= base_url('assets/plugins/bootstrap/js/bootstrap.min.js') ?>"></script>
    
    <script>
        $(document).ready(function() {
            // Hide loading overlay when page is ready
            $('#loadingOverlay').fadeOut();
            
            // Show loading overlay on form submit
            $('form').on('submit', function() {
                $('#loadingOverlay').fadeIn();
            });
            
            // Auto-hide alerts after 5 seconds
            $('.alert').delay(5000).fadeOut();
            
            // Add some interactive effects
            $('.login-container').hover(
                function() {
                    $(this).css('transform', 'translateY(-5px)');
                },
                function() {
                    $(this).css('transform', 'translateY(0)');
                }
            );
        });
    </script>
</body>
</html>
