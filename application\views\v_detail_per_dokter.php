<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <meta name="author" content="" />
    <title>Detail Per Dokter - Monitoring DPJP Praktek</title>
    <link href="https://fonts.googleapis.com/css?family=Poppins:400,500,700,800&display=swap" rel="stylesheet">
    <link href="assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/plugins/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="assets/plugins/perfectscroll/perfect-scrollbar.css" rel="stylesheet">
    <link href="assets/plugins/DataTables/datatables.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css" rel="stylesheet">

    <!-- Theme Styles -->
    <link href="assets/css/main.css" rel="stylesheet">
    <link href="assets/css/custom.css" rel="stylesheet">
</head>

<style>
    /* DataTables Buttons Styling */
    .dt-buttons {
        margin-bottom: 15px;
    }

    .dt-button {
        margin-right: 5px !important;
        border-radius: 4px !important;
        font-size: 12px !important;
        padding: 6px 12px !important;
    }

    .dt-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .summary-card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }

    .summary-card:hover {
        transform: translateY(-2px);
    }

    .performance-badge {
        font-size: 0.9em;
        padding: 0.5em 1em;
    }
</style>
<body>
<div class="page-container">
    <div class="page-header">
        <nav class="navbar navbar-expand-lg d-flex justify-content-between">
            <div class="row" style="width:100%">
                <div class="col-md-4 col-sm-12 d-flex justify-content-start">
                    <h3 class="mt-3">DETAIL PER DOKTER - MONITORING DPJP</h3>
                </div>
                <div class="col-md-4 col-sm-12 d-flex justify-content-center">
                    <div id="tanggalku" style="font-size: 40px;"></div>
                </div>
                <div class="col-md-4 col-sm-12 d-flex justify-content-end pull-right">
                    <div class="row">
                        <div class="col-12">
                            <h4 style="text-align: end;"><?= date("l, d F Y") ?></h4>
                        </div>
                        <div class="col-12">
                            <h3 style="text-align: end;" class="total_dokter"><b>Total Dokter ()</b></h3>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </div>

    <div class="page-content" style="margin-top:0px">
        <div class="main-wrapper">
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card summary-card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">Total Dokter</h4>
                            <h2 id="total_dokter_count">0</h2>
                            <small><i class="fas fa-user-md"></i> Dokter Aktif</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card summary-card bg-info text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">Total Records</h4>
                            <h2 id="total_records_count">0</h2>
                            <small><i class="fas fa-list"></i> Total Data</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card summary-card bg-success text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">Dengan Appointment</h4>
                            <h2 id="total_dengan_appointment_count">0</h2>
                            <small><i class="fas fa-calendar-check"></i> Records</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card summary-card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">Tanpa Appointment</h4>
                            <h2 id="total_tanpa_appointment_count">0</h2>
                            <small><i class="fas fa-calendar-times"></i> Records</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Summary Row -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">Dengan Data Mulai</h4>
                            <h2 id="total_dengan_data_count">0</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h4 class="card-title">Tanpa Data Mulai</h4>
                            <h2 id="total_tanpa_data_count">0</h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Section -->
            <div class="row">
                <div class="col-md-12 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <form id="filterForm" class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Tanggal Awal</label>
                                        <input type="datetime-local" class="form-control" id="tgl_awal" name="tgl_awal" value="<?= date('Y-m-d\T00:00') ?>">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Tanggal Akhir</label>
                                        <input type="datetime-local" class="form-control" id="tgl_akhir" name="tgl_akhir" value="<?= date('Y-m-d\T23:59') ?>">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <button type="button" id="btnFilter" class="btn btn-primary form-control">
                                            <i class="fas fa-search"></i> Filter Data
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Table -->
            <div class="row">
                <div class="col">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="detailDokterTable" class="display" style="width:100%">
                                    <thead>
                                        <tr style="text-align:center">
                                            <th>ID Dokter</th>
                                            <th>Nama Dokter</th>
                                            <th>Ruangan Praktek</th>
                                            <th>Ruangan Perjanjian</th>
                                            <th>Tanggal Pendaftaran</th>
                                            <th>Tanggal Perjanjian</th>
                                            <th>Jadwal Mulai Praktek</th>
                                            <th>Jam Boarding</th>
                                            <th>Jam Mulai</th>
                                            <th>Jam CPPT</th>
                                            <th>Durasi</th>
                                            <th>Kedatangan</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="assets/plugins/jquery/jquery-3.4.1.min.js"></script>
<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="assets/plugins/bootstrap/js/bootstrap.min.js"></script>
<script src="https://unpkg.com/feather-icons"></script>
<script src="assets/plugins/perfectscroll/perfect-scrollbar.min.js"></script>
<script src="assets/plugins/DataTables/datatables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="assets/js/main.min.js"></script>

<script>
$(document).ready(function() {
    console.log('Document ready, initializing Detail Per Dokter...');

    // Get base URL dynamically
    var currentPath = window.location.pathname;
    var baseUrl = '';

    if (currentPath.includes('index.php')) {
        // If using index.php in URL
        baseUrl = window.location.origin + currentPath.split('/index.php')[0] + '/index.php/';
    } else {
        // If using clean URLs
        baseUrl = window.location.origin + '/monitoring_cendana/';
    }
    console.log('Base URL:', baseUrl);

    var table = $('#detailDokterTable').DataTable({
        responsive: true,
        processing: true,
        serverSide: false,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'pdfHtml5',
                text: '<i class="fas fa-file-pdf"></i> Export PDF',
                className: 'btn btn-danger btn-sm',
                title: 'Detail Per Dokter - ' + new Date().toLocaleDateString('id-ID'),
                orientation: 'landscape',
                pageSize: 'A3',
                exportOptions: {
                    columns: ':visible'
                },
                customize: function(doc) {
                    doc.defaultStyle.fontSize = 7;
                    doc.styles.tableHeader.fontSize = 8;
                    doc.styles.tableHeader.fillColor = '#2c3e50';
                }
            },
            {
                extend: 'excelHtml5',
                text: '<i class="fas fa-file-excel"></i> Export Excel',
                className: 'btn btn-success btn-sm',
                title: 'Detail Per Dokter - ' + new Date().toLocaleDateString('id-ID'),
                exportOptions: {
                    columns: ':visible'
                }
            }
        ],
        ajax: {
            url: baseUrl + 'DetailPerDokter/get_detail_per_dokter_data',
            type: 'POST',
            data: function(d) {
                d.tgl_awal = $('#tgl_awal').val();
                d.tgl_akhir = $('#tgl_akhir').val();
                console.log('Sending data:', d);
            },
            dataSrc: function(json) {
                console.log('DataTable response:', json);
                if (json.error) {
                    console.error('Server error:', json.error);
                    alert('Error: ' + json.error);
                    return [];
                }

                // Update total count
                if (json.recordsTotal !== undefined) {
                    $(".total_dokter").text('Total Dokter (' + json.recordsTotal + ')');
                    loadSummaryData();
                }

                return json.data || [];
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                console.error('Response:', xhr.responseText);
                alert('AJAX Error: ' + error + '\nCheck console for details');
            }
        },
        columnDefs: [
            {
                targets: [0, 3, 4, 5, 6, 7, 8, 9, 10], // Center align specific columns
                className: 'text-center'
            },
            {
                targets: [11], // Kedatangan column
                render: function(data, type, row) {
                    if (data === 'Tepat Waktu') {
                        return '<span class="badge bg-success">Tepat Waktu</span>';
                    } else if (data === 'Terlambat') {
                        return '<span class="badge bg-danger">Terlambat</span>';
                    } else if (data === 'Lebih Awal') {
                        return '<span class="badge bg-info">Lebih Awal</span>';
                    } else if (data === 'Tidak Ada Data') {
                        return '<span class="badge bg-warning text-dark">Tidak Ada Data</span>';
                    } else {
                        return '<span class="badge bg-secondary">Tidak Ada Data</span>';
                    }
                },
                className: 'text-center'
            }
        ],

        ordering: true,
        searching: true,
        lengthChange: true,
        pageLength: 100,
        lengthMenu: [25, 50, 100, 200],
        order: [[1, 'asc']], // Order by doctor name ascending
        initComplete: function(settings, json) {
            console.log('DataTable initialization complete');
            console.log('Settings:', settings);
            console.log('Initial JSON:', json);
        },
        drawCallback: function(settings) {
            console.log('DataTable draw callback triggered');
            console.log('Rows displayed:', this.api().rows().count());
        }
    });

    console.log('DataTable object created:', table);

    $('#btnFilter').on('click', function() {
        console.log('Filter button clicked');
        table.ajax.reload();
    });

    function loadSummaryData() {
        console.log('Loading summary data...');
        var currentPath = window.location.pathname;
        var baseUrl = '';

        if (currentPath.includes('index.php')) {
            baseUrl = window.location.origin + currentPath.split('/index.php')[0] + '/index.php/';
        } else {
            baseUrl = window.location.origin + '/monitoring_cendana/';
        }
        $.ajax({
            url: baseUrl + 'DetailPerDokter/get_summary_data',
            type: 'POST',
            data: {
                tgl_awal: $('#tgl_awal').val(),
                tgl_akhir: $('#tgl_akhir').val()
            },
            success: function(response) {
                console.log('Summary response:', response);
                try {
                    var data = JSON.parse(response);
                    $('#total_dokter_count').text(data.total_dokter || 0);
                    $('#total_records_count').text(data.total_records || 0);
                    $('#total_dengan_appointment_count').text(data.total_dengan_appointment || 0);
                    $('#total_tanpa_appointment_count').text(data.total_tanpa_appointment || 0);
                    $('#total_dengan_data_count').text(data.total_dengan_data || 0);
                    $('#total_tanpa_data_count').text(data.total_tanpa_data || 0);
                } catch (e) {
                    console.error('Error parsing summary response:', e);
                }
            },
            error: function(xhr, status, error) {
                console.error('Summary AJAX Error:', status, error);
                console.error('Summary Response:', xhr.responseText);
            }
        });
    }

    function reloadDatatable() {
        $('#detailDokterTable').DataTable().ajax.reload(null, false);
    }

    setInterval(reloadDatatable, 300000); // Refresh every 5 minutes
});
</script>

<script type="text/javascript">
    window.setTimeout("waktu()", 1000);
    function waktu() {
        var tanggal = new Date();
        setTimeout("waktu()", 1000);
        document.getElementById("tanggalku").innerHTML = tanggal.getHours() + " : " + tanggal.getMinutes() + " : " + tanggal.getSeconds();
    }
</script>
</body>
</html>
