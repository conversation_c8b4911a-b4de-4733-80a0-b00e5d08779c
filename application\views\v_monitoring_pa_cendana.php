
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <meta name="author" content="" />
    <title>Dashboard Monitoring HASIL LAB PA Gedung Cendana</title>
    <link href="https://fonts.googleapis.com/css?family=Poppins:400,500,700,800&display=swap" rel="stylesheet">
    <link href="assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/plugins/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="assets/plugins/perfectscroll/perfect-scrollbar.css" rel="stylesheet">
    <link href="assets/plugins/DataTables/datatables.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css" rel="stylesheet">

    <!-- Theme Styles -->
    <link href="assets/css/main.css" rel="stylesheet">
    <link href="assets/css/custom.css" rel="stylesheet">
</head>

<style>
    .hijau {
        background-color: #10ac84;
        color: white;
    }

    .kuning{
        background: #feb019d9;
        color: white;
    }

    .merah {
        background-color: #c9001dd9;
        color: white;
    }

    .orange {
        background-color: #ff6d00;
        color: white;
    }

    /* DataTables Buttons Styling */
    .dt-buttons {
        margin-bottom: 15px;
    }

    .dt-button {
        margin-right: 5px !important;
        border-radius: 4px !important;
        font-size: 12px !important;
        padding: 6px 12px !important;
    }

    .dt-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
</style>

<body>
<div class="page-container">
    <div class="page-header">
        <nav class="navbar navbar-expand-lg d-flex justify-content-between">
            <div class="row" style="width:100%">
                <div class="col-md-4 col-sm-12 d-flex justify-content-start"><h3 class="mt-3">MONITORING HASIL LAB PA GEDUNG CENDANA</h3></div>
                <div class="col-md-4 col-sm-12 d-flex justify-content-center"><div id="tanggalku" style="font-size: 40px;"></div></div>
                <div class="col-md-4 col-sm-12 d-flex justify-content-end pull-right">
                    <div class="row">
                        <div class="col-12">
                            <h4 style="text-align: end;"><?= date("l, d F Y") ?></h4>
                        </div>
                        <div class="col-12">
                            <h3 style="text-align: end;" class="total_pasien"><b>Total Pemeriksaan ()</b></h3>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </div>
    <div class="page-content" style="margin-top:0px">
        <div class="main-wrapper">
            <div class="row">
                <div class="col-md-12 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <form id="filterForm" class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Tanggal Awal</label>
                                        <input type="datetime-local" class="form-control" id="tgl_awal" name="tgl_awal" value="<?= date('Y-m-d 00:00:00') ?>">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Tanggal Akhir</label>
                                        <input type="datetime-local" class="form-control" id="tgl_akhir" name="tgl_akhir" value="<?= date('Y-m-d 23:59:59') ?>">
                                    </div>
                                </div>
                                   <div class="col-md-3">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <button type="button" id="btnFilter" class="btn btn-primary form-control">Filter</button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>Filter Status</label>
                                        <select class="form-control" id="status_filter" name="status_filter">
                                            <option value="">Semua Status</option>
                                            <option value="hijau" style="background-color: #10ac84; color: white;">🟢 Hijau (Final + Tepat Waktu)</option>
                                            <option value="kuning" style="background-color: #feb019d9; color: white;">🟡 Kuning (Final + Perhatian)</option>
                                            <option value="merah" style="background-color: #c9001dd9; color: white;">🔴 Merah (Final + Terlambat)</option>
                                            <option value="abu_abu" style="background-color: #33b2ff; color: white;">🔵 Biru (Belum Final + Tepat Waktu)</option>
                                            <option value="cream" style="background-color: #9f9ca5; color: white;">⚫ Abu-abu (Belum Final + Perhatian)</option>
                                            <option value="hitam" style="background-color: #000000; color: white;">⚫ Hitam (Belum Final + Terlambat)</option>
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="myTable" class="display" style="width:100%">
                                    <thead>
                                        <tr style="text-align:center">
                                            <th>No. Lab</th>
                                            <th>Nama Pasien</th>
                                            <th>No. RM</th>
                                            <th>DOKTER</th>
                                            <th>Ruang Asal</th>
                                            <th>Masuk Sampel</th>
                                            <th>Tanggal Hasil</th>
                                            <th style="text-align: center;">Lama Pemeriksaan</th>
                                            <th>Jenis</th>
                                            <th>Status</th>
                                            <th>Status LIS</th>
                                            <th>JENIS PEMERIKSAAN</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="assets/plugins/jquery/jquery-3.4.1.min.js"></script>
<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="assets/plugins/bootstrap/js/bootstrap.min.js"></script>
<script src="https://unpkg.com/feather-icons"></script>
<script src="assets/plugins/perfectscroll/perfect-scrollbar.min.js"></script>
<script src="assets/plugins/DataTables/datatables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="assets/js/main.min.js"></script>
<script src="assets/js/pages/datatables.js"></script>

<script>
$(document).ready(function() {
    var table = $('#myTable').DataTable({
        responsive: true,
        processing: true,
        serverSide: false,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'pdfHtml5',
                text: '<i class="fas fa-file-pdf"></i> Export PDF',
                className: 'btn btn-danger btn-sm',
                title: 'Monitoring PA Cendana - ' + new Date().toLocaleDateString('id-ID'),
                orientation: 'landscape',
                pageSize: 'A4',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11] // Exclude Status LIS column (index 10)
                },
                customize: function(doc) {
                    doc.defaultStyle.fontSize = 8;
                    doc.styles.tableHeader.fontSize = 9;
                    doc.styles.tableHeader.fillColor = '#2c3e50';
                    doc.content[1].table.widths = ['8%', '15%', '8%', '12%', '10%', '12%', '12%', '8%', '8%', '7%', '10%'];
                }
            },
            {
                extend: 'excelHtml5',
                text: '<i class="fas fa-file-excel"></i> Export Excel',
                className: 'btn btn-success btn-sm',
                title: 'Monitoring PA Cendana - ' + new Date().toLocaleDateString('id-ID'),
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11]
                }
            }
        ],
        ajax: {
            url: 'MonitoringPaCendana/get_histo_sito_data',
            type: 'POST',
            data: function(d) {
                d.tgl_awal = $('#tgl_awal').val();
                d.tgl_akhir = $('#tgl_akhir').val();
            },
            complete: function(data) {
                var res = data['responseJSON'];
                $(".total_pasien").text('Total Pemeriksaan (' + res['recordsTotal'] + ')');
            },
        },
        columnDefs: [
            {
                targets: [7], // Lama Pemeriksaan column
                className: 'text-center'
            },
            {
                targets: [10], // Status LIS column
                render: function(data, type, row) {
                    // Check if FNL (column index 9) is not "Final"
                    if (row[9] !== 'Final') {
                        return '<span class="badge bg-info">On Progress</span>';
                    }
                    
                    if (data === 'Green') {
                        return '<span class="badge bg-success">Tepat Waktu</span>';
                    } else if (data === 'Yellow') {
                        return '<span class="badge bg-warning">Perhatian</span>';
                    } else if (data === 'Red') {
                        return '<span class="badge bg-danger">Terlambat</span>';
                    } else {
                        return data;
                    }
                }
            }
        ],
        createdRow: function(row, data, dataIndex) {
            // Check if FNL (data[9]) is not "Final"
            if (data[9] !== 'Final') {
                if (data[10] === 'Red') {
                    $(row).css('background-color', '#000000'); // Black for belum final + red
                    $(row).css('color', '#ffffff'); // White text for readability
                } else if (data[10] === 'Yellow') {
                    $(row).css('background-color', '#9f9ca5'); // Cream for belum final + yellow
                      $(row).css('color', '#ffffff'); // White text for readability
                } else if (data[10] === 'Green') {
                    $(row).css('background-color', '#33b2ff'); // Gray for belum final + green
                    $(row).css('color', '#ffffff'); // White text for readability
                }
            } else {
                // Original colors for Final status
                if (data[10] === 'Red') {
                    $(row).addClass("merah");
                } else if (data[10] === 'Yellow') {
                    $(row).addClass("kuning");
                } else if (data[10] === 'Green') {
                    $(row).addClass("hijau");
                }
            }
        },
        ordering: true,
        searching: true,
        lengthChange: true,
        pageLength: 200,
        lengthMenu: [10, 25, 50, 100, 200]
    });

    $('#btnFilter').on('click', function() {
        table.ajax.reload();
    });

    // Status filter functionality based on background color
    $('#status_filter').on('change', function() {
        var filterValue = $(this).val();

        // Show all rows first
        $('#myTable tbody tr').show();

        if (filterValue !== '') {
            $('#myTable tbody tr').each(function() {
                var $row = $(this);
                var backgroundColor = $row.css('background-color');
                var shouldShow = false;
                
                // Convert RGB to hex for comparison (if needed)
                var rgbToHex = function(rgb) {
                    if (rgb.indexOf('#') === 0) return rgb;
                    var rgbArray = rgb.match(/\d+/g);
                    if (!rgbArray) return rgb;
                    return '#' + ((1 << 24) + (parseInt(rgbArray[0]) << 16) + (parseInt(rgbArray[1]) << 8) + parseInt(rgbArray[2])).toString(16).slice(1);
                };

                var hexColor = rgbToHex(backgroundColor);

                switch(filterValue) {
                    case 'hijau':
                        shouldShow = $row.hasClass('hijau') || hexColor === '#10ac84';
                        break;
                    case 'kuning':
                        shouldShow = $row.hasClass('kuning') || backgroundColor.includes('254, 176, 25');
                        break;
                    case 'merah':
                        shouldShow = $row.hasClass('merah') || hexColor === '#c9001d';
                        break;
                    case 'abu_abu':
                        shouldShow = hexColor === '#33b2ff' || backgroundColor.includes('51, 178, 255');
                        break;
                    case 'cream':
                        shouldShow = hexColor === '#9f9ca5' || backgroundColor.includes('159, 156, 165');
                        break;
                    case 'hitam':
                        shouldShow = hexColor === '#000000' || backgroundColor.includes('0, 0, 0');
                        break;
                }
                
                if (!shouldShow) {
                    $row.hide();
                }
            });
        }
    });

    function reloadDatatable() {
        $('#myTable').DataTable().ajax.reload(null, false);
    }

    setInterval(reloadDatatable, 300000); // Refresh every minute
});
</script>

<script type="text/javascript">
    window.setTimeout("waktu()", 1000);
    function waktu() {
        var tanggal = new Date();
        setTimeout("waktu()", 1000);
        document.getElementById("tanggalku").innerHTML = tanggal.getHours() + " : " + tanggal.getMinutes() + " : " + tanggal.getSeconds();
    }
</script>
</body>
</html>

















