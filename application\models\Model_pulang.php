
<?php
class Model_pulang extends CI_Model{

    function listPasien(){
        // $ins = $this->session->userdata('ses_ins');
        $hasil= "CALL layanan.DashboardIGD()";
        $bind = $this->db->query($hasil);
        return $bind;
    }

    function listFarmasi(){
        $hasil = "CALL layanan.dasborSafarjanAktifPasien()";
        $bind = $this->db->query($hasil);
        return $bind;
    }
    
    function listHistoSito($tglAwal, $tglAkhir){
        // Format dates to ensure they're in the correct format (YYYY-MM-DD HH:MM:SS)
        $tglAwal = date('Y-m-d H:i:s', strtotime($tglAwal));
        $tglAkhir = date('Y-m-d H:i:s', strtotime($tglAkhir));
        
        $sql = "SELECT a.* 
            , IF(0=0,'Semua',(SELECT ref.DESKRIPSI FROM master.referensi ref WHERE ref.ID=0 AND ref.JENIS=10)) CARABAYARHEADER
        FROM
        (SELECT 
            pa.NOMOR_LAB NOPA, pk.NOMOR KUNJUNGAN, pk.NOPEN, pa.NORM NORM, ref.DESKRIPSI PENJAMIN,
            master.getNamaLengkap(pa.NORM) NAMA_PASIEN, master.getNamaLengkapPegawai(mp.NIP) DSPA, 
            mr2.DESKRIPSI RUANG_ASAL, master.getNamaTindakan2(pk.NOMOR) TINDAKAN,
            pk.MASUK MASUK_KUNJUNGAN, pa.TANGGAL_LAB MASUK_SAMPEL, pa.TANGGAL TANGGAL_HASIL,
            IF((master.getJumlahHariKerja(DATE(pa.TANGGAL_LAB), DATE(pa.TANGGAL))-1)<0,0,
                (master.getJumlahHariKerja(DATE(pa.TANGGAL_LAB), DATE(pa.TANGGAL))-1)) LAMA_PEMERIKSAAN,
            SUBSTR(pa.TANGGAL_LAB,9,2) TANGGALSAMPLE, SUBSTR(pa.TANGGAL_LAB,6,2) BULANSAMPEL,
            SUBSTR(pa.TANGGAL,9,2) TANGGALJAWAB, SUBSTR(pa.TANGGAL,6,2) BULANJAWAB,
            INST.NAMAINST, INST.ALAMATINST, master.getHeaderLaporan('105021201') INSTALASI,
            IF(0=0,'Semua', master.getNamaLengkapPegawai(mp.NIP)) DOKTERHEADER,
            IF(0=0,'Semua',t.NAMA) TINDAKANHEADER, 'Histologi' JENIZ, '' NOLABSLMBNYA,
            pa.DIAGNOSA_KLINIK, pa.KETERANGAN_KLINIK, pa.MIKROSKOPIK, pa.MAKROSKOPIK,
            pa.IMUNO_HISTOKIMIA IHK, pa.KESIMPULAN, IF(pa.`STATUS`=1,'Belum Final','Final') FNL,
            IF(pa.`STATUS`=2,(master.getJumlahHariKerja(DATE(pa.TANGGAL_LAB), DATE(pa.TANGGAL))-1),
                (master.getJumlahHariKerja(DATE(pa.TANGGAL_LAB), DATE(NOW()))-1)) DURASI_HARI_KERJA,
            IF(pa.JENIS_PEMERIKSAAN = 1, 'REGULER', IF(pa.JENIS_PEMERIKSAAN = 2, 'VC','KONSUL/REVIEW')) JENIS_PERIKSAAN,
            IF(pa.JENIS_JARINGAN = 1, 'JARINGAN KECIL', IF(pa.JENIS_JARINGAN = 2, 'JARINGAN BESAR', '-')) JENIS_SEDIAAN,
            IF(pa.`STATUS`=2, 
                IF(pa.JENIS_JARINGAN IN (0,2),
                    IF((master.getJumlahHariKerja(DATE(pa.TANGGAL_LAB), DATE(pa.TANGGAL))-1)<=4, 'Green',
                        IF((master.getJumlahHariKerja(DATE(pa.TANGGAL_LAB), DATE(pa.TANGGAL))-1)<=6,'Yellow','Red')),
                    IF((master.getJumlahHariKerja(DATE(pa.TANGGAL_LAB), DATE(pa.TANGGAL))-1)<=1, 'Green',
                        IF((master.getJumlahHariKerja(DATE(pa.TANGGAL_LAB), DATE(pa.TANGGAL))-1)=2,'Yellow','Red'))),
                IF(pa.JENIS_JARINGAN IN (0,2),
                    IF((master.getJumlahHariKerja(DATE(pa.TANGGAL_LAB), DATE(NOW()))-1)<=4, 'Green',
                        IF((master.getJumlahHariKerja(DATE(pa.TANGGAL_LAB), DATE(NOW()))-1)<=6,'Yellow','Red')),
                    IF((master.getJumlahHariKerja(DATE(pa.TANGGAL_LAB), DATE(NOW()))-1)<=1, 'Green',
                        IF((master.getJumlahHariKerja(DATE(pa.TANGGAL_LAB), DATE(NOW()))-1)=2,'Yellow','Red')))) STATUS_LIS,
            IF(pa.STATUS_LIS = 1, 'Diterima Data Entri',
                IF(pa.STATUS_LIS = 3 AND pa.JENIS_PEMERIKSAAN IN (3),'Diterima Data Entri',
                    IF(pa.STATUS_LIS = 2 AND pa.JENIS_PEMERIKSAAN IN (1,2),'Diterima Ruang Potong',
                        IF(pa.STATUS_LIS = 3 AND pa.JENIS_PEMERIKSAAN IN (1,2), 'Final Ruang Potong',
                            IF(pa.STATUS_LIS IN (4) AND pa.JENIS_PEMERIKSAAN IN (1,3), 'Diterima Ruang Histologi',
                                IF(pa.STATUS_LIS IN (5) AND pa.JENIS_PEMERIKSAAN IN (1,3), 'Final Ruang Histologi',
                                    IF(pa.STATUS_LIS = 5 AND pa.JENIS_PEMERIKSAAN IN (1,2), 'Final Ruang Potong',
                                        IF(pa.STATUS_LIS=6,'Diterima Dokter','')))))))) AS STATUS_PROSES
        FROM layanan.hasil_pa_histologi pa 
            LEFT JOIN master.dokter md ON pa.DOKTER = md.ID
            LEFT JOIN master.pegawai mp ON md.NIP = mp.NIP
            LEFT JOIN pendaftaran.kunjungan pk ON pa.KUNJUNGAN = pk.NOMOR
            LEFT JOIN master.ruangan mr ON pk.RUANGAN = mr.ID
            LEFT JOIN layanan.tindakan_medis tm ON pa.KUNJUNGAN = tm.KUNJUNGAN
            LEFT JOIN master.tindakan t ON tm.TINDAKAN=t.ID	
            LEFT JOIN layanan.order_lab ol ON ol.NOMOR = pk.REF
            LEFT JOIN pendaftaran.kunjungan pk2 ON pk2.NOMOR = ol.KUNJUNGAN
            LEFT JOIN master.ruangan mr2 ON pk2.RUANGAN = mr2.ID 
            LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
            LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR 
            LEFT JOIN master.referensi ref ON ref.ID = pj.JENIS AND ref.JENIS=10
            , (SELECT p.NAMA NAMAINST, p.ALAMAT ALAMATINST
                FROM aplikasi.instansi ai, master.ppk p
                WHERE ai.PPK=p.ID) INST
        WHERE pa.NORM IS NOT NULL
            AND IF(pa.TANGGAL_LAB > '2018-08-23 00:00:00', pa.`STATUS` !=0, pa.`STATUS` in (1,2))
            AND pa.TANGGAL_LAB BETWEEN ? AND ?
            AND pk.NOPEN IS NOT NULL
            AND mr2.GEDUNG = 1
            AND pj.JENIS !=2
        GROUP BY pa.NOMOR_LAB

        /* Sitologi section - similar structure */
        /* Imuno Histokimia section - similar structure */
        ) a
        ORDER BY a.MASUK_SAMPEL";
        
        $bind = $this->db->query($sql, array($tglAwal, $tglAkhir));
        return $bind;
    }
    function lisdpjppraktek($tglAwal, $tglAkhir){
        // Format dates to ensure they're in the correct format (YYYY-MM-DD HH:MM:SS)
        $tglAwal = date('Y-m-d H:i:s', strtotime($tglAwal));
        $tglAkhir = date('Y-m-d H:i:s', strtotime($tglAkhir));
        
        $sql = "SELECT p.NORM, 
                    master.getNamaLengkap(p.NORM) PASIEN, 
                    IF(DATE_FORMAT(ps.TANGGAL,'%d-%m-%Y')=DATE_FORMAT(pk.MASUK,'%d-%m-%Y'),'Baru','Lama') STATUSPENGUNJUNG,
                    IF(perj.ID IS NULL, master.getNamaLengkapPegawai(dokt.NIP), master.getNamaLengkapPegawai(dokper.NIP)) DOKTER,
                    ru.DESKRIPSI RUANGAN_PRAKTEK,
                    IF(rper.DESKRIPSI IS NOT NULL, rper.DESKRIPSI, 'TIDAK ADA APPOINTMENT') RUANGAN_PERJANJIAN,
                    DATE_FORMAT(p.TANGGAL,'%d-%m-%Y %H:%i:%s') TANGGAL_PENDAFTARAN,
                    IF(perj.TANGGAL IS NOT NULL, DATE_FORMAT(perj.TANGGAL,'%d-%m-%Y'), 'TIDAK ADA APPOINTMENT') TANGGAL_RENCANA_PERJANJIAN,
                    IF(perj.TANGGAL IS NOT NULL,CONCAT(perj.TANGGAL, ' ', STR_TO_DATE(jad.AWAL,'%H:%i:%s')), 'TIDAK ADA APPOINTMENT') JADWAL_MULAI_PRAKTEK,
                    IF(bor2.created_at IS NOT NULL,DATE_FORMAT(bor2.created_at,'%d-%m-%Y %H:%i:%s'),'TIDAK ADA DATA') JAM_BOARDING_2,
                    IF(pelpas.waktumulai IS NOT NULL,DATE_FORMAT(pelpas.waktumulai,'%d-%m-%Y %H:%i:%s'),'TIDAK ADA DATA') JAM_WAKTU_MULAI,
                    IF(cp.tanggal IS NOT NULL, DATE_FORMAT(cp.tanggal,'%d-%m-%Y %H:%i:%s'),'TIDAK ADA DATA') JAM_CPPT,
                    IF(
                        perj.TANGGAL IS NOT NULL AND pelpas.waktumulai IS NOT NULL,
                        TIME_FORMAT(
                            TIMEDIFF(
                                pelpas.waktumulai,
                                CONCAT(perj.TANGGAL, ' ', jad.AWAL)
                            ), '%H:%i:%s'
                        ),
                        'TIDAK ADA DATA'
                    ) DURASI_MULAI_VS_JADWAL,
                    IF(
                        perj.TANGGAL IS NULL OR pelpas.waktumulai IS NULL,
                        'TIDAK ADA DATA',
                        IF(
                            TIMESTAMPDIFF(MINUTE, CONCAT(perj.TANGGAL, ' ', jad.AWAL), pelpas.waktumulai) < 0,
                            'Lebih Awal',
                            IF(
                                TIMESTAMPDIFF(MINUTE, CONCAT(perj.TANGGAL, ' ', jad.AWAL), pelpas.waktumulai) <= 60,
                                'Tepat Waktu',
                                'Terlambat'
                            )
                        )
                    ) KEDATANGAN

                FROM pendaftaran.pendaftaran p
                LEFT JOIN remun_medis.perjanjian perj ON perj.NOMR = p.NORM AND perj.TANGGAL = DATE(p.TANGGAL) AND perj.STATUS != 0
                LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
                LEFT JOIN master.referensi refpj ON refpj.ID = pj.JENIS AND refpj.JENIS = 10
                LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
                LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR AND pk.REF IS NULL AND pk.STATUS != 0 AND pk.RUANGAN = tp.RUANGAN
                LEFT JOIN layanan.order_resep ore ON ore.KUNJUNGAN = pk.NOMOR AND ore.STATUS = 2
                LEFT JOIN pendaftaran.kunjungan pkr ON pkr.REF = ore.NOMOR
                LEFT JOIN layanan.scanfarmasi scf ON scf.nokun = pkr.NOMOR
                LEFT JOIN master.ruangan ru ON ru.ID = tp.RUANGAN
                LEFT JOIN master.dokter dokt ON dokt.ID = tp.DOKTER
                LEFT JOIN master.dokter dokper ON dokper.ID = perj.ID_DOKTER
                LEFT JOIN master.pegawai peg ON peg.NIP = dokper.NIP
                LEFT JOIN master.referensi smfper ON smfper.ID = peg.SMF AND smfper.JENIS = 26
                LEFT JOIN master.ruangan rper ON rper.ID = perj.ID_RUANGAN
                LEFT JOIN remun_medis.jadwal jad ON jad.DOKTER = perj.ID_DOKTER AND perj.ID_RUANGAN = jad.RUANGAN
                    AND jad.TANGGAL = perj.TANGGAL AND jad.STATUS != 0
                LEFT JOIN db_layanan.tb_boarding bor ON bor.nomr = p.NORM AND DATE(bor.created_at) = DATE(p.TANGGAL)
                LEFT JOIN db_layanan.tb_boarding_2 bor2 ON bor2.nomr = p.NORM AND DATE(bor2.created_at) = DATE(p.TANGGAL)
                LEFT JOIN keperawatan.tb_cppt cp ON cp.nokun = pk.NOMOR AND cp.status != 0 AND cp.pemberi_cppt = 1
                LEFT JOIN remun_medis.pelayanan_pasien pelpas ON pelpas.nokun = pk.NOMOR
                LEFT JOIN master.pasien ps ON ps.NORM = p.NORM
                LEFT JOIN antrianv2.antrian aa ON aa.nokun = pk.NOMOR AND aa.status != 0
                LEFT JOIN antrianv2.m_jenis_antrian aja ON aja.id = aa.id_jenis_antrian AND aja.id_unit = 1

                WHERE p.TANGGAL BETWEEN ? AND ?
                    AND p.STATUS != 0 
                    AND ru.JENIS_KUNJUNGAN IN (15)
                    AND ru.ID IN (105021101,105021102,105021103,105021104,105021201)
                GROUP BY pk.NOMOR
                ORDER BY p.TANGGAL DESC";

        $bind = $this->db->query($sql, array($tglAwal, $tglAkhir));
        return $bind;
    }

    function lisdpjppraktek_tepat_waktu($tglAwal, $tglAkhir){
        // Format dates to ensure they're in the correct format (YYYY-MM-DD HH:MM:SS)
        $tglAwal = date('Y-m-d H:i:s', strtotime($tglAwal));
        $tglAkhir = date('Y-m-d H:i:s', strtotime($tglAkhir));

        $sql = "SELECT p.NORM,
                    master.getNamaLengkap(p.NORM) PASIEN,
                    IF(DATE_FORMAT(ps.TANGGAL,'%d-%m-%Y')=DATE_FORMAT(pk.MASUK,'%d-%m-%Y'),'Baru','Lama') STATUSPENGUNJUNG,
                    IF(perj.ID IS NULL, master.getNamaLengkapPegawai(dokt.NIP), master.getNamaLengkapPegawai(dokper.NIP)) DOKTER,
                    ru.DESKRIPSI RUANGAN_PRAKTEK,
                    IF(rper.DESKRIPSI IS NOT NULL, rper.DESKRIPSI, 'TIDAK ADA APPOINTMENT') RUANGAN_PERJANJIAN,
                    DATE_FORMAT(p.TANGGAL,'%d-%m-%Y %H:%i:%s') TANGGAL_PENDAFTARAN,
                    IF(perj.TANGGAL IS NOT NULL, DATE_FORMAT(perj.TANGGAL,'%d-%m-%Y'), 'TIDAK ADA APPOINTMENT') TANGGAL_RENCANA_PERJANJIAN,
                    IF(perj.TANGGAL IS NOT NULL,CONCAT(perj.TANGGAL, ' ', STR_TO_DATE(jad.AWAL,'%H:%i:%s')), 'TIDAK ADA APPOINTMENT') JADWAL_MULAI_PRAKTEK,
                    IF(bor2.created_at IS NOT NULL,DATE_FORMAT(bor2.created_at,'%d-%m-%Y %H:%i:%s'),'TIDAK ADA DATA') JAM_BOARDING_2,
                    IF(pelpas.waktumulai IS NOT NULL,DATE_FORMAT(pelpas.waktumulai,'%d-%m-%Y %H:%i:%s'),'TIDAK ADA DATA') JAM_WAKTU_MULAI,
                    IF(cp.tanggal IS NOT NULL, DATE_FORMAT(cp.tanggal,'%d-%m-%Y %H:%i:%s'),'TIDAK ADA DATA') JAM_CPPT,
                    IF(
                        perj.TANGGAL IS NOT NULL AND pelpas.waktumulai IS NOT NULL,
                        TIME_FORMAT(
                            TIMEDIFF(
                                pelpas.waktumulai,
                                CONCAT(perj.TANGGAL, ' ', jad.AWAL)
                            ), '%H:%i:%s'
                        ),
                        'TIDAK ADA DATA'
                    ) DURASI_MULAI_VS_JADWAL,
                    'Tepat Waktu' KEDATANGAN

                FROM pendaftaran.pendaftaran p
                LEFT JOIN remun_medis.perjanjian perj ON perj.NOMR = p.NORM AND perj.TANGGAL = DATE(p.TANGGAL) AND perj.STATUS != 0
                LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
                LEFT JOIN master.referensi refpj ON refpj.ID = pj.JENIS AND refpj.JENIS = 10
                LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
                LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR AND pk.REF IS NULL AND pk.STATUS != 0 AND pk.RUANGAN = tp.RUANGAN
                LEFT JOIN layanan.order_resep ore ON ore.KUNJUNGAN = pk.NOMOR AND ore.STATUS = 2
                LEFT JOIN pendaftaran.kunjungan pkr ON pkr.REF = ore.NOMOR
                LEFT JOIN layanan.scanfarmasi scf ON scf.nokun = pkr.NOMOR
                LEFT JOIN master.ruangan ru ON ru.ID = tp.RUANGAN
                LEFT JOIN master.dokter dokt ON dokt.ID = tp.DOKTER
                LEFT JOIN master.dokter dokper ON dokper.ID = perj.ID_DOKTER
                LEFT JOIN master.pegawai peg ON peg.NIP = dokper.NIP
                LEFT JOIN master.referensi smfper ON smfper.ID = peg.SMF AND smfper.JENIS = 26
                LEFT JOIN master.ruangan rper ON rper.ID = perj.ID_RUANGAN
                LEFT JOIN remun_medis.jadwal jad ON jad.DOKTER = perj.ID_DOKTER AND perj.ID_RUANGAN = jad.RUANGAN
                    AND jad.TANGGAL = perj.TANGGAL AND jad.STATUS != 0
                LEFT JOIN db_layanan.tb_boarding bor ON bor.nomr = p.NORM AND DATE(bor.created_at) = DATE(p.TANGGAL)
                LEFT JOIN db_layanan.tb_boarding_2 bor2 ON bor2.nomr = p.NORM AND DATE(bor2.created_at) = DATE(p.TANGGAL)
                LEFT JOIN keperawatan.tb_cppt cp ON cp.nokun = pk.NOMOR AND cp.status != 0 AND cp.pemberi_cppt = 1
                LEFT JOIN remun_medis.pelayanan_pasien pelpas ON pelpas.nokun = pk.NOMOR
                LEFT JOIN master.pasien ps ON ps.NORM = p.NORM
                LEFT JOIN antrianv2.antrian aa ON aa.nokun = pk.NOMOR AND aa.status != 0
                LEFT JOIN antrianv2.m_jenis_antrian aja ON aja.id = aa.id_jenis_antrian AND aja.id_unit = 1

                WHERE p.TANGGAL BETWEEN ? AND ?
                    AND p.STATUS != 0
                    AND ru.JENIS_KUNJUNGAN IN (15)
                    AND ru.ID IN (105021101,105021102,105021103,105021104,105021201)
                    AND perj.TANGGAL IS NOT NULL
                    AND pelpas.waktumulai IS NOT NULL
                    AND TIMESTAMPDIFF(MINUTE, CONCAT(perj.TANGGAL, ' ', jad.AWAL), pelpas.waktumulai) >= 0
                    AND TIMESTAMPDIFF(MINUTE, CONCAT(perj.TANGGAL, ' ', jad.AWAL), pelpas.waktumulai) <= 60
                GROUP BY pk.NOMOR
                ORDER BY p.TANGGAL DESC";

        $bind = $this->db->query($sql, array($tglAwal, $tglAkhir));
        return $bind;
    }

    function lisdpjppraktek_terlambat($tglAwal, $tglAkhir){
        // Format dates to ensure they're in the correct format (YYYY-MM-DD HH:MM:SS)
        $tglAwal = date('Y-m-d H:i:s', strtotime($tglAwal));
        $tglAkhir = date('Y-m-d H:i:s', strtotime($tglAkhir));

        $sql = "SELECT p.NORM,
                    master.getNamaLengkap(p.NORM) PASIEN,
                    IF(DATE_FORMAT(ps.TANGGAL,'%d-%m-%Y')=DATE_FORMAT(pk.MASUK,'%d-%m-%Y'),'Baru','Lama') STATUSPENGUNJUNG,
                    IF(perj.ID IS NULL, master.getNamaLengkapPegawai(dokt.NIP), master.getNamaLengkapPegawai(dokper.NIP)) DOKTER,
                    ru.DESKRIPSI RUANGAN_PRAKTEK,
                    IF(rper.DESKRIPSI IS NOT NULL, rper.DESKRIPSI, 'TIDAK ADA APPOINTMENT') RUANGAN_PERJANJIAN,
                    DATE_FORMAT(p.TANGGAL,'%d-%m-%Y %H:%i:%s') TANGGAL_PENDAFTARAN,
                    IF(perj.TANGGAL IS NOT NULL, DATE_FORMAT(perj.TANGGAL,'%d-%m-%Y'), 'TIDAK ADA APPOINTMENT') TANGGAL_RENCANA_PERJANJIAN,
                    IF(perj.TANGGAL IS NOT NULL,CONCAT(perj.TANGGAL, ' ', STR_TO_DATE(jad.AWAL,'%H:%i:%s')), 'TIDAK ADA APPOINTMENT') JADWAL_MULAI_PRAKTEK,
                    IF(bor2.created_at IS NOT NULL,DATE_FORMAT(bor2.created_at,'%d-%m-%Y %H:%i:%s'),'TIDAK ADA DATA') JAM_BOARDING_2,
                    IF(pelpas.waktumulai IS NOT NULL,DATE_FORMAT(pelpas.waktumulai,'%d-%m-%Y %H:%i:%s'),'TIDAK ADA DATA') JAM_WAKTU_MULAI,
                    IF(cp.tanggal IS NOT NULL, DATE_FORMAT(cp.tanggal,'%d-%m-%Y %H:%i:%s'),'TIDAK ADA DATA') JAM_CPPT,
                    IF(
                        perj.TANGGAL IS NOT NULL AND pelpas.waktumulai IS NOT NULL,
                        TIME_FORMAT(
                            TIMEDIFF(
                                pelpas.waktumulai,
                                CONCAT(perj.TANGGAL, ' ', jad.AWAL)
                            ), '%H:%i:%s'
                        ),
                        'TIDAK ADA DATA'
                    ) DURASI_MULAI_VS_JADWAL,
                    'Terlambat' KEDATANGAN

                FROM pendaftaran.pendaftaran p
                LEFT JOIN remun_medis.perjanjian perj ON perj.NOMR = p.NORM AND perj.TANGGAL = DATE(p.TANGGAL) AND perj.STATUS != 0
                LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
                LEFT JOIN master.referensi refpj ON refpj.ID = pj.JENIS AND refpj.JENIS = 10
                LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
                LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR AND pk.REF IS NULL AND pk.STATUS != 0 AND pk.RUANGAN = tp.RUANGAN
                LEFT JOIN layanan.order_resep ore ON ore.KUNJUNGAN = pk.NOMOR AND ore.STATUS = 2
                LEFT JOIN pendaftaran.kunjungan pkr ON pkr.REF = ore.NOMOR
                LEFT JOIN layanan.scanfarmasi scf ON scf.nokun = pkr.NOMOR
                LEFT JOIN master.ruangan ru ON ru.ID = tp.RUANGAN
                LEFT JOIN master.dokter dokt ON dokt.ID = tp.DOKTER
                LEFT JOIN master.dokter dokper ON dokper.ID = perj.ID_DOKTER
                LEFT JOIN master.pegawai peg ON peg.NIP = dokper.NIP
                LEFT JOIN master.referensi smfper ON smfper.ID = peg.SMF AND smfper.JENIS = 26
                LEFT JOIN master.ruangan rper ON rper.ID = perj.ID_RUANGAN
                LEFT JOIN remun_medis.jadwal jad ON jad.DOKTER = perj.ID_DOKTER AND perj.ID_RUANGAN = jad.RUANGAN
                    AND jad.TANGGAL = perj.TANGGAL AND jad.STATUS != 0
                LEFT JOIN db_layanan.tb_boarding bor ON bor.nomr = p.NORM AND DATE(bor.created_at) = DATE(p.TANGGAL)
                LEFT JOIN db_layanan.tb_boarding_2 bor2 ON bor2.nomr = p.NORM AND DATE(bor2.created_at) = DATE(p.TANGGAL)
                LEFT JOIN keperawatan.tb_cppt cp ON cp.nokun = pk.NOMOR AND cp.status != 0 AND cp.pemberi_cppt = 1
                LEFT JOIN remun_medis.pelayanan_pasien pelpas ON pelpas.nokun = pk.NOMOR
                LEFT JOIN master.pasien ps ON ps.NORM = p.NORM
                LEFT JOIN antrianv2.antrian aa ON aa.nokun = pk.NOMOR AND aa.status != 0
                LEFT JOIN antrianv2.m_jenis_antrian aja ON aja.id = aa.id_jenis_antrian AND aja.id_unit = 1

                WHERE p.TANGGAL BETWEEN ? AND ?
                    AND p.STATUS != 0
                    AND ru.JENIS_KUNJUNGAN IN (15)
                    AND ru.ID IN (105021101,105021102,105021103,105021104,105021201)
                    AND perj.TANGGAL IS NOT NULL
                    AND pelpas.waktumulai IS NOT NULL
                    AND TIMESTAMPDIFF(MINUTE, CONCAT(perj.TANGGAL, ' ', jad.AWAL), pelpas.waktumulai) > 60
                GROUP BY pk.NOMOR
                ORDER BY p.TANGGAL DESC";

        $bind = $this->db->query($sql, array($tglAwal, $tglAkhir));
        return $bind;
    }

    function lisdpjppraktek_lebih_awal($tglAwal, $tglAkhir){
        // Format dates to ensure they're in the correct format (YYYY-MM-DD HH:MM:SS)
        $tglAwal = date('Y-m-d H:i:s', strtotime($tglAwal));
        $tglAkhir = date('Y-m-d H:i:s', strtotime($tglAkhir));

        $sql = "SELECT p.NORM,
                    master.getNamaLengkap(p.NORM) PASIEN,
                    IF(DATE_FORMAT(ps.TANGGAL,'%d-%m-%Y')=DATE_FORMAT(pk.MASUK,'%d-%m-%Y'),'Baru','Lama') STATUSPENGUNJUNG,
                    IF(perj.ID IS NULL, master.getNamaLengkapPegawai(dokt.NIP), master.getNamaLengkapPegawai(dokper.NIP)) DOKTER,
                    ru.DESKRIPSI RUANGAN_PRAKTEK,
                    IF(rper.DESKRIPSI IS NOT NULL, rper.DESKRIPSI, 'TIDAK ADA APPOINTMENT') RUANGAN_PERJANJIAN,
                    DATE_FORMAT(p.TANGGAL,'%d-%m-%Y %H:%i:%s') TANGGAL_PENDAFTARAN,
                    IF(perj.TANGGAL IS NOT NULL, DATE_FORMAT(perj.TANGGAL,'%d-%m-%Y'), 'TIDAK ADA APPOINTMENT') TANGGAL_RENCANA_PERJANJIAN,
                    IF(perj.TANGGAL IS NOT NULL,CONCAT(perj.TANGGAL, ' ', STR_TO_DATE(jad.AWAL,'%H:%i:%s')), 'TIDAK ADA APPOINTMENT') JADWAL_MULAI_PRAKTEK,
                    IF(bor2.created_at IS NOT NULL,DATE_FORMAT(bor2.created_at,'%d-%m-%Y %H:%i:%s'),'TIDAK ADA DATA') JAM_BOARDING_2,
                    IF(pelpas.waktumulai IS NOT NULL,DATE_FORMAT(pelpas.waktumulai,'%d-%m-%Y %H:%i:%s'),'TIDAK ADA DATA') JAM_WAKTU_MULAI,
                    IF(cp.tanggal IS NOT NULL, DATE_FORMAT(cp.tanggal,'%d-%m-%Y %H:%i:%s'),'TIDAK ADA DATA') JAM_CPPT,
                    IF(
                        perj.TANGGAL IS NOT NULL AND pelpas.waktumulai IS NOT NULL,
                        TIME_FORMAT(
                            TIMEDIFF(
                                pelpas.waktumulai,
                                CONCAT(perj.TANGGAL, ' ', jad.AWAL)
                            ), '%H:%i:%s'
                        ),
                        'TIDAK ADA DATA'
                    ) DURASI_MULAI_VS_JADWAL,
                    'Lebih Awal' KEDATANGAN

                FROM pendaftaran.pendaftaran p
                LEFT JOIN remun_medis.perjanjian perj ON perj.NOMR = p.NORM AND perj.TANGGAL = DATE(p.TANGGAL) AND perj.STATUS != 0
                LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
                LEFT JOIN master.referensi refpj ON refpj.ID = pj.JENIS AND refpj.JENIS = 10
                LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
                LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR AND pk.REF IS NULL AND pk.STATUS != 0 AND pk.RUANGAN = tp.RUANGAN
                LEFT JOIN layanan.order_resep ore ON ore.KUNJUNGAN = pk.NOMOR AND ore.STATUS = 2
                LEFT JOIN pendaftaran.kunjungan pkr ON pkr.REF = ore.NOMOR
                LEFT JOIN layanan.scanfarmasi scf ON scf.nokun = pkr.NOMOR
                LEFT JOIN master.ruangan ru ON ru.ID = tp.RUANGAN
                LEFT JOIN master.dokter dokt ON dokt.ID = tp.DOKTER
                LEFT JOIN master.dokter dokper ON dokper.ID = perj.ID_DOKTER
                LEFT JOIN master.pegawai peg ON peg.NIP = dokper.NIP
                LEFT JOIN master.referensi smfper ON smfper.ID = peg.SMF AND smfper.JENIS = 26
                LEFT JOIN master.ruangan rper ON rper.ID = perj.ID_RUANGAN
                LEFT JOIN remun_medis.jadwal jad ON jad.DOKTER = perj.ID_DOKTER AND perj.ID_RUANGAN = jad.RUANGAN
                    AND jad.TANGGAL = perj.TANGGAL AND jad.STATUS != 0
                LEFT JOIN db_layanan.tb_boarding bor ON bor.nomr = p.NORM AND DATE(bor.created_at) = DATE(p.TANGGAL)
                LEFT JOIN db_layanan.tb_boarding_2 bor2 ON bor2.nomr = p.NORM AND DATE(bor2.created_at) = DATE(p.TANGGAL)
                LEFT JOIN keperawatan.tb_cppt cp ON cp.nokun = pk.NOMOR AND cp.status != 0 AND cp.pemberi_cppt = 1
                LEFT JOIN remun_medis.pelayanan_pasien pelpas ON pelpas.nokun = pk.NOMOR
                LEFT JOIN master.pasien ps ON ps.NORM = p.NORM
                LEFT JOIN antrianv2.antrian aa ON aa.nokun = pk.NOMOR AND aa.status != 0
                LEFT JOIN antrianv2.m_jenis_antrian aja ON aja.id = aa.id_jenis_antrian AND aja.id_unit = 1

                WHERE p.TANGGAL BETWEEN ? AND ?
                    AND p.STATUS != 0
                    AND ru.JENIS_KUNJUNGAN IN (15)
                    AND ru.ID IN (105021101,105021102,105021103,105021104,105021201)
                    AND perj.TANGGAL IS NOT NULL
                    AND pelpas.waktumulai IS NOT NULL
                    AND TIMESTAMPDIFF(MINUTE, CONCAT(perj.TANGGAL, ' ', jad.AWAL), pelpas.waktumulai) < 0
                GROUP BY pk.NOMOR
                ORDER BY p.TANGGAL DESC";

        $bind = $this->db->query($sql, array($tglAwal, $tglAkhir));
        return $bind;
    }

    function ketepatanDpjpPraktek($tglAwal, $tglAkhir){
        // Format dates to ensure they're in the correct format (YYYY-MM-DD HH:MM:SS)
        $tglAwal = date('Y-m-d H:i:s', strtotime($tglAwal));
        $tglAkhir = date('Y-m-d H:i:s', strtotime($tglAkhir));
        
        $sql = "SELECT 
            DATA.DOKTERID,
            DATA.DOKTER,
            # DATA.JADWAL_MULAI_PRAKTEK,
            COUNT(*) AS TOTAL_PRAKTEK,
            SUM(DATA.KEDATANGAN = 'Lebih Awal') AS JUMLAH_LEBIH_AWAL,
            SUM(DATA.KEDATANGAN = 'Tepat Waktu') AS JUMLAH_TEPAT_WAKTU,
            SUM(DATA.KEDATANGAN = 'Terlambat') AS JUMLAH_TERLAMBAT
            FROM (
            SELECT 
            DATA.DOKTERID,
            DATA.DOKTER,
            DATA.JADWAL_MULAI_PRAKTEK,
            #COUNT(*) AS TOTAL_PASIEN,
            DATA.WAKTU_MULAI_PRAKTEK,
            DATA.KEDATANGAN
            # SUM(DATA.KEDATANGAN = 'Lebih Awal') AS JUMLAH_LEBIH_AWAL,
            #SUM(DATA.KEDATANGAN = 'Tepat Waktu') AS JUMLAH_TEPAT_WAKTU,
            #SUM(DATA.KEDATANGAN = 'Terlambat') AS JUMLAH_TERLAMBAT
            FROM (
            SELECT 
                IF(perj.ID IS NULL, dokt.ID, dokper.ID) AS DOKTERID,
                IF(perj.ID IS NULL, master.getNamaLengkapPegawai(dokt.NIP), master.getNamaLengkapPegawai(dokper.NIP)) AS DOKTER,
                CONCAT(jad.TANGGAL, ' ', STR_TO_DATE(jad.AWAL,'%H:%i:%s')) AS JADWAL_MULAI_PRAKTEK,
                pelpas.waktumulai AS WAKTU_MULAI_PRAKTEK,
                perj.TANGGAL,
                jad.AWAL,
                -- Klasifikasi kedatangan
                IF(
                jad.TANGGAL IS NULL OR pelpas.waktumulai IS NULL,
                'TIDAK ADA DATA',
                IF(
                    TIMESTAMPDIFF(MINUTE, CONCAT(jad.TANGGAL, ' ', jad.AWAL), pelpas.waktumulai) < 0,
                    'Lebih Awal',
                    IF(
                    TIMESTAMPDIFF(MINUTE, CONCAT(jad.TANGGAL, ' ', jad.AWAL), pelpas.waktumulai) <= 60,
                    'Tepat Waktu',
                    'Terlambat'
                    )
                )
                ) AS KEDATANGAN
            FROM pendaftaran.pendaftaran p
            LEFT JOIN remun_medis.perjanjian perj ON perj.NOMR = p.NORM AND perj.TANGGAL = DATE(p.TANGGAL) AND perj.STATUS != 0
            LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR AND pk.REF IS NULL AND pk.STATUS != 0 AND pk.RUANGAN = tp.RUANGAN
            LEFT JOIN master.ruangan ru ON ru.ID = tp.RUANGAN
            LEFT JOIN master.dokter dokt ON dokt.ID = tp.DOKTER
            LEFT JOIN master.dokter dokper ON dokper.ID = perj.ID_DOKTER
            LEFT JOIN remun_medis.jadwal jad ON jad.DOKTER = perj.ID_DOKTER AND perj.ID_RUANGAN = jad.RUANGAN 
                AND jad.TANGGAL = perj.TANGGAL AND jad.STATUS != 0
            LEFT JOIN keperawatan.tb_cppt cp ON cp.nokun = pk.NOMOR AND cp.status != 0 AND cp.pemberi_cppt = 1
            LEFT JOIN remun_medis.pelayanan_pasien pelpas ON pelpas.nokun = pk.NOMOR

            WHERE p.TANGGAL BETWEEN ? AND ?
                AND p.STATUS != 0
                AND ru.JENIS_KUNJUNGAN IN (15)
                AND ru.ID IN (105021101, 105021102, 105021103, 105021104, 105021201)
                AND jad.RUANGAN = tp.RUANGAN
                AND (dokt.ID NOT IN (397) OR dokper.ID NOT IN (397))
                AND cp.tanggal IS NOT NULL
            ) AS DATA
            WHERE DATA.KEDATANGAN IN ('Tepat Waktu', 'Terlambat', 'Lebih Awal')
            GROUP BY DATA.DOKTERID, DATA.DOKTER, DATA.JADWAL_MULAI_PRAKTEK
            ORDER BY DATA.DOKTERID, DATA.WAKTU_MULAI_PRAKTEK

            ) AS DATA
            WHERE DATA.KEDATANGAN IN ('Tepat Waktu', 'Terlambat', 'Lebih Awal')
            GROUP BY DATA.DOKTERID, DATA.DOKTER# DATA.JADWAL_MULAI_PRAKTEK
            ORDER BY DATA.DOKTER, DATA.JADWAL_MULAI_PRAKTEK;
            ";

        $bind = $this->db->query($sql, array($tglAwal, $tglAkhir));
        return $bind;
    }

    function ketepatanDpjpPraktekNew($tglAwal, $tglAkhir){
        // Format dates to ensure they're in the correct format (YYYY-MM-DD HH:MM:SS)
        $tglAwal = date('Y-m-d H:i:s', strtotime($tglAwal));
        $tglAkhir = date('Y-m-d H:i:s', strtotime($tglAkhir));

        $sql = "SELECT
            DATA.DOKTERID,
            DATA.DOKTER,
            COUNT(*) AS TOTAL_PRAKTEK,
            SUM(DATA.KEDATANGAN = 'Lebih Awal') AS JUMLAH_LEBIH_AWAL,
            SUM(DATA.KEDATANGAN = 'Tepat Waktu') AS JUMLAH_TEPAT_WAKTU,
            SUM(DATA.KEDATANGAN = 'Terlambat') AS JUMLAH_TERLAMBAT
            FROM (
            SELECT
            DATA.DOKTERID,
            DATA.DOKTER,
            DATA.JADWAL_MULAI_PRAKTEK,
            DATA.WAKTU_MULAI_PRAKTEK,
            DATA.KEDATANGAN
            FROM (
            SELECT
                IF(perj.ID IS NULL, dokt.ID, dokper.ID) AS DOKTERID,
                IF(perj.ID IS NULL, master.getNamaLengkapPegawai(dokt.NIP), master.getNamaLengkapPegawai(dokper.NIP)) AS DOKTER,
                CONCAT(jad.TANGGAL, ' ', STR_TO_DATE(jad.AWAL,'%H:%i:%s')) AS JADWAL_MULAI_PRAKTEK,
                pelpas.waktumulai AS WAKTU_MULAI_PRAKTEK,
                perj.TANGGAL,
                jad.AWAL,
                -- Klasifikasi kedatangan
                IF(
                jad.TANGGAL IS NULL OR pelpas.waktumulai IS NULL,
                'TIDAK ADA DATA',
                IF(
                    TIMESTAMPDIFF(MINUTE, CONCAT(jad.TANGGAL, ' ', jad.AWAL), pelpas.waktumulai) < 0,
                    'Lebih Awal',
                    IF(
                    TIMESTAMPDIFF(MINUTE, CONCAT(jad.TANGGAL, ' ', jad.AWAL), pelpas.waktumulai) <= 60,
                    'Tepat Waktu',
                    'Terlambat'
                    )
                )
                ) AS KEDATANGAN
            FROM pendaftaran.pendaftaran p
            LEFT JOIN remun_medis.perjanjian perj ON perj.NOMR = p.NORM AND perj.TANGGAL = DATE(p.TANGGAL) AND perj.STATUS != 0
            LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR AND pk.REF IS NULL AND pk.STATUS != 0 AND pk.RUANGAN = tp.RUANGAN
            LEFT JOIN master.ruangan ru ON ru.ID = tp.RUANGAN
            LEFT JOIN master.dokter dokt ON dokt.ID = tp.DOKTER
            LEFT JOIN master.dokter dokper ON dokper.ID = perj.ID_DOKTER
            LEFT JOIN remun_medis.jadwal jad ON jad.DOKTER = perj.ID_DOKTER AND perj.ID_RUANGAN = jad.RUANGAN
                AND jad.TANGGAL = perj.TANGGAL AND jad.STATUS != 0
            LEFT JOIN keperawatan.tb_cppt cp ON cp.nokun = pk.NOMOR AND cp.status != 0 AND cp.pemberi_cppt = 1
            LEFT JOIN remun_medis.pelayanan_pasien pelpas ON pelpas.nokun = pk.NOMOR

            WHERE p.TANGGAL BETWEEN ? AND ?
                AND p.STATUS != 0
                AND ru.JENIS_KUNJUNGAN IN (15)
                AND jad.RUANGAN = tp.RUANGAN
                AND ru.ID IN (105021101, 105021102, 105021103, 105021104, 105021201)
                AND (dokt.ID NOT IN (397) OR dokper.ID NOT IN (397))
                AND cp.tanggal IS NOT NULL
            ) AS DATA
            WHERE DATA.KEDATANGAN IN ('Tepat Waktu', 'Terlambat', 'Lebih Awal')
            GROUP BY DATA.DOKTERID, DATA.DOKTER, DATA.JADWAL_MULAI_PRAKTEK
            ORDER BY DATA.DOKTERID, DATA.WAKTU_MULAI_PRAKTEK

            ) AS DATA
            WHERE DATA.KEDATANGAN IN ('Tepat Waktu', 'Terlambat', 'Lebih Awal')
            GROUP BY DATA.DOKTERID, DATA.DOKTER
            ORDER BY DATA.DOKTERID;
            ";

        $bind = $this->db->query($sql, array($tglAwal, $tglAkhir));
        return $bind;
    }

    function rekap_per_dokter($tglAwal, $tglAkhir){
        // Format dates to ensure they're in the correct format (YYYY-MM-DD HH:MM:SS)
        $tglAwal = date('Y-m-d H:i:s', strtotime($tglAwal));
        $tglAkhir = date('Y-m-d H:i:s', strtotime($tglAkhir));

        $sql = "SELECT
            DATA.DOKTERID,
            DATA.DOKTER,
            COUNT(*) AS TOTAL_PRAKTEK,
            SUM(DATA.KEDATANGAN = 'Lebih Awal') AS JUMLAH_LEBIH_AWAL,
            SUM(DATA.KEDATANGAN = 'Tepat Waktu') AS JUMLAH_TEPAT_WAKTU,
            SUM(DATA.KEDATANGAN = 'Terlambat') AS JUMLAH_TERLAMBAT,
            SUM(DATA.KEDATANGAN = 'TIDAK ADA DATA') AS JUMLAH_TIDAK_ADA_DATA
            FROM (
            SELECT
            DATA.DOKTERID,
            DATA.DOKTER,
            DATA.JADWAL_MULAI_PRAKTEK,
            DATA.WAKTU_MULAI_PRAKTEK,
            DATA.KEDATANGAN
            FROM (
            SELECT
                IF(perj.ID IS NULL, dokt.ID, dokper.ID) AS DOKTERID,
                IF(perj.ID IS NULL, master.getNamaLengkapPegawai(dokt.NIP), master.getNamaLengkapPegawai(dokper.NIP)) AS DOKTER,
                CONCAT(perj.TANGGAL, ' ', STR_TO_DATE(jad.AWAL,'%H:%i:%s')) AS JADWAL_MULAI_PRAKTEK,
                pelpas.waktumulai AS WAKTU_MULAI_PRAKTEK,
                perj.TANGGAL,
                jad.AWAL,
                -- Klasifikasi kedatangan
                IF(
                jad.TANGGAL IS NULL OR pelpas.waktumulai IS NULL,
                'TIDAK ADA DATA',
                IF(
                    TIMESTAMPDIFF(MINUTE, CONCAT(jad.TANGGAL, ' ', jad.AWAL), pelpas.waktumulai) < 0,
                    'Lebih Awal',
                    IF(
                    TIMESTAMPDIFF(MINUTE, CONCAT(jad.TANGGAL, ' ', jad.AWAL), pelpas.waktumulai) <= 60,
                    'Tepat Waktu',
                    'Terlambat'
                    )
                )
                ) AS KEDATANGAN
            FROM pendaftaran.pendaftaran p
            LEFT JOIN remun_medis.perjanjian perj ON perj.NOMR = p.NORM AND perj.TANGGAL = DATE(p.TANGGAL) AND perj.STATUS != 0
            LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
            LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = p.NOMOR AND pk.REF IS NULL AND pk.STATUS != 0 AND pk.RUANGAN = tp.RUANGAN
            LEFT JOIN master.ruangan ru ON ru.ID = tp.RUANGAN
            LEFT JOIN master.dokter dokt ON dokt.ID = tp.DOKTER
            LEFT JOIN master.dokter dokper ON dokper.ID = perj.ID_DOKTER
            LEFT JOIN remun_medis.jadwal jad ON jad.DOKTER = perj.ID_DOKTER AND perj.ID_RUANGAN = jad.RUANGAN
                AND jad.TANGGAL = perj.TANGGAL AND jad.STATUS != 0
            LEFT JOIN keperawatan.tb_cppt cp ON cp.nokun = pk.NOMOR AND cp.status != 0 AND cp.pemberi_cppt = 1
            LEFT JOIN remun_medis.pelayanan_pasien pelpas ON pelpas.nokun = pk.NOMOR

            WHERE p.TANGGAL BETWEEN ? AND ?
                AND p.STATUS != 0
                AND ru.JENIS_KUNJUNGAN IN (15)
                AND jad.RUANGAN = tp.RUANGAN
                AND ru.ID IN (105021101, 105021102, 105021103, 105021104, 105021201)
                AND (dokt.ID NOT IN (397) OR dokper.ID NOT IN (397))
                AND cp.tanggal IS NOT NULL
            ) AS DATA
            WHERE DATA.KEDATANGAN IN ('Tepat Waktu', 'Terlambat', 'Lebih Awal')
            GROUP BY DATA.DOKTERID, DATA.DOKTER, DATA.JADWAL_MULAI_PRAKTEK
            ORDER BY DATA.DOKTERID, DATA.WAKTU_MULAI_PRAKTEK

            ) AS DATA
            GROUP BY DATA.DOKTERID, DATA.DOKTER
            ORDER BY DATA.DOKTERID;
        ";

        $bind = $this->db->query($sql, array($tglAwal, $tglAkhir));
        return $bind;
    }

    function perjanjian($tglAwal, $tglAkhir){
        // Format dates to ensure they're in the correct format (YYYY-MM-DD HH:MM:SS)
        $tglAwal = date('Y-m-d H:i:s', strtotime($tglAwal));
        $tglAkhir = date('Y-m-d H:i:s', strtotime($tglAkhir));

        $sql = "SELECT
            `rp`.`NOMR`,
            `rp`.`NAMAPASIEN`,
            IF(DATE(pp.TANGGAL) = DATE(mpa.TANGGAL) OR rp.NOMR = 0, 'Baru', 'Lama') status,
            UPPER(`mr`.`DESKRIPSI`) RUANGANDESK,
            CONCAT(rr.DESKRIPSI, ' (', IF(`rp`.STATUS_BERKUNJUNG = 1, 'Berkunjung Ke RS', 'Telemedicine'), ')') RENCANA,
            rp.NOMOR,
            CONCAT(
                IF(TRIM(ap.NIP) = '' OR ap.NIP IS NULL, 'Pendaftaran Web', `master`.getNamaLengkapPegawai(ap.NIP)),
                ' [', rp.INPUT, ']'
            ) AS OLEH,
            (SELECT mrp.DESKRIPSI FROM pendaftaran.pendaftaran p
             LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
             LEFT JOIN `master`.referensi mrp ON pj.JENIS = mrp.ID AND mrp.JENIS = 10
             WHERE p.NORM = rp.NOMR AND p.`STATUS` != 0
             ORDER BY p.TANGGAL DESC LIMIT 1) PENJAMIN,
            IF(mpa.`STATUS` = 2, 'Meninggal', IF(mpa.`STATUS` = 0, 'Non Aktif', 'Hidup')) STATUS_PASIEN,
            IF(rp.STATUS_SORE = 1, 'Poli Sore', 'Poli Pagi') KET

        FROM `remun_medis`.`perjanjian` `rp`
        LEFT JOIN pendaftaran.pendaftaran pp ON rp.NOMR = pp.NORM AND rp.TANGGAL = DATE(pp.TANGGAL)
        LEFT JOIN `master`.pasien mpa ON rp.NOMR = mpa.NORM
        LEFT JOIN `master`.diagnosa_masuk mdm ON pp.DIAGNOSA_MASUK = mdm.ID
        LEFT JOIN `master`.`ruangan` `mr` ON `rp`.`ID_RUANGAN` = `mr`.`ID` AND `mr`.`JENIS` = 5
        LEFT JOIN `master`.`dokter` `md` ON `md`.`ID` = `rp`.`ID_DOKTER`
        LEFT JOIN `master`.`pegawai` `mp` ON `mp`.`NIP` = `md`.`NIP`
        LEFT JOIN aplikasi.pengguna ap ON ap.ID = rp.OLEH
        LEFT JOIN `remun_medis`.rencana rr ON rp.RENCANA = rr.ID
        LEFT JOIN bpjs.peserta sr ON sr.norm = rp.NOMR
        LEFT JOIN bpjs.suratkontrol tol ON tol.noKartu = sr.noKartu AND rp.TANGGAL = tol.tglRencanaKontrol AND tol.`status` = 1
        LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = pp.NOMOR
        LEFT JOIN master.referensi kls ON kls.ID = pj.KELAS AND kls.JENIS = 19
        WHERE `mr`.`JENIS_KUNJUNGAN` = 15
            AND rp.STATUS != 0
            AND rp.TANGGAL BETWEEN ? AND ?
        GROUP BY rp.NOMR, rp.NOMOR
        ORDER BY md.ID, mr.ID, rp.RENCANA, rp.NOKONTROLDOKTER ASC";

        $bind = $this->db->query($sql, array($tglAwal, $tglAkhir));
        return $bind;
    }
}
?>
