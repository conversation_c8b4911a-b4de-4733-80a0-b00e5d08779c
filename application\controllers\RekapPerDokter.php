<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class RekapPerDokter extends CI_Controller {

    function __construct()
    {
        parent::__construct();
        date_default_timezone_set("Asia/Bangkok");
        $this->load->helper('url');
        $this->load->model('Model_pulang');
    }

    public function index() {
        $this->load->view('v_rekap_per_dokter');
    }

    public function test_connection() {
        echo "Controller is working! Current time: " . date('Y-m-d H:i:s');
    }

    public function get_rekap_per_dokter_data() {
        try {
            $tglAwal = $this->input->post('tgl_awal') ?? date('Y-m-d 00:00:00');
            $tglAkhir = $this->input->post('tgl_akhir') ?? date('Y-m-d 23:59:59');

            // Debug: Log the parameters
            log_message('debug', 'Rekap Per Dokter - tglAwal: ' . $tglAwal . ', tglAkhir: ' . $tglAkhir);

            $listData = $this->Model_pulang->rekap_per_dokter($tglAwal, $tglAkhir);

            if (!$listData) {
                throw new Exception('Query failed or returned null');
            }

            $data = array();
            $totalRows = $listData->num_rows();

            // Debug: Log the number of rows
            log_message('debug', 'Rekap Per Dokter - Total rows: ' . $totalRows);

            if ($totalRows > 0) {
                $nomor = 1;
                foreach ($listData->result() as $row) {
                    // Calculate total patients and percentage
                    $totalPasien = ($row->JUMLAH_LEBIH_AWAL ?? 0) + ($row->JUMLAH_TEPAT_WAKTU ?? 0) +
                                  ($row->JUMLAH_TERLAMBAT ?? 0) + ($row->JUMLAH_TIDAK_ADA_DATA ?? 0);

                    $persentaseTepatWaktu = $totalPasien > 0 ?
                        round(((($row->JUMLAH_TEPAT_WAKTU ?? 0) + ($row->JUMLAH_LEBIH_AWAL ?? 0)) / $totalPasien) * 100, 2) : 0;

                    $data[] = array(
                        $nomor++,
                        $row->DOKTER ?? '',
                        $totalPasien,
                        $row->JUMLAH_LEBIH_AWAL ?? 0,
                        $row->JUMLAH_TEPAT_WAKTU ?? 0,
                        $row->JUMLAH_TERLAMBAT ?? 0,
                        $persentaseTepatWaktu
                    );
                }
            }

            $output = array(
                "draw" => intval($this->input->post("draw")),
                "recordsTotal" => $totalRows,
                "recordsFiltered" => $totalRows,
                "data" => $data
            );

            echo json_encode($output);

        } catch (Exception $e) {
            log_message('error', 'Rekap Per Dokter Error: ' . $e->getMessage());

            $output = array(
                "draw" => intval($this->input->post("draw")),
                "recordsTotal" => 0,
                "recordsFiltered" => 0,
                "data" => array(),
                "error" => $e->getMessage()
            );

            echo json_encode($output);
        }
    }

    public function get_summary_data() {
        try {
            $tglAwal = $this->input->post('tgl_awal') ?? date('Y-m-d 00:00:00');
            $tglAkhir = $this->input->post('tgl_akhir') ?? date('Y-m-d 23:59:59');

            $listData = $this->Model_pulang->rekap_per_dokter($tglAwal, $tglAkhir);

            $summary = array(
                'total_dokter' => 0,
                'total_pasien' => 0,
                'total_tepat_waktu' => 0,
                'total_terlambat' => 0,
                'total_lebih_awal' => 0,
                'total_tidak_ada_data' => 0,
                'rata_rata_persentase' => 0
            );

            if ($listData && $listData->num_rows() > 0) {
                $summary['total_dokter'] = $listData->num_rows();
                $total_persentase = 0;
                
                foreach ($listData->result() as $row) {
                    $summary['total_tepat_waktu'] += $row->JUMLAH_TEPAT_WAKTU ?? 0;
                    $summary['total_terlambat'] += $row->JUMLAH_TERLAMBAT ?? 0;
                    $summary['total_lebih_awal'] += $row->JUMLAH_LEBIH_AWAL ?? 0;
                    $summary['total_tidak_ada_data'] += $row->JUMLAH_TIDAK_ADA_DATA ?? 0;
                    
                    // Calculate total patients for this doctor
                    $totalPasienDokter = ($row->JUMLAH_LEBIH_AWAL ?? 0) + ($row->JUMLAH_TEPAT_WAKTU ?? 0) + 
                                        ($row->JUMLAH_TERLAMBAT ?? 0) + ($row->JUMLAH_TIDAK_ADA_DATA ?? 0);
                    $summary['total_pasien'] += $totalPasienDokter;
                    
                    // Calculate percentage for this doctor (including Tepat Waktu + Lebih Awal)
                    if ($totalPasienDokter > 0) {
                        $persentaseDokter = ((($row->JUMLAH_TEPAT_WAKTU ?? 0) + ($row->JUMLAH_LEBIH_AWAL ?? 0)) / $totalPasienDokter) * 100;
                        $total_persentase += $persentaseDokter;
                    }
                }
                
                $summary['rata_rata_persentase'] = $summary['total_dokter'] > 0 ? 
                    round($total_persentase / $summary['total_dokter'], 2) : 0;
            }

            echo json_encode($summary);

        } catch (Exception $e) {
            log_message('error', 'Rekap Per Dokter Summary Error: ' . $e->getMessage());

            $summary = array(
                'total_dokter' => 0,
                'total_pasien' => 0,
                'total_tepat_waktu' => 0,
                'total_terlambat' => 0,
                'total_lebih_awal' => 0,
                'total_tidak_ada_data' => 0,
                'rata_rata_persentase' => 0,
                'error' => $e->getMessage()
            );

            echo json_encode($summary);
        }
    }

    public function export_excel() {
        // Excel export functionality - to be implemented
        echo "Excel export functionality - coming soon";
    }
}
