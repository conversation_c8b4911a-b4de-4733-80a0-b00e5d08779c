<style>
    /* DataTables Buttons Styling */
    .dt-buttons {
        margin-bottom: 15px;
    }
    
    .dt-button {
        margin-right: 5px !important;
        border-radius: 4px !important;
        font-size: 12px !important;
        padding: 6px 12px !important;
    }
    
    .dt-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    .summary-card {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    
    .summary-card:hover {
        transform: translateY(-2px);
    }

    .status-badge {
        font-size: 0.8em;
        padding: 0.3em 0.6em;
    }
</style>

<!-- Page Title Section -->
<div class="container-fluid py-3">
    <div class="row">
        <div class="col-md-6">
            <h3 class="mb-0">MONITORING - DATA PERJANJIAN</h3>
        </div>
        <div class="col-md-3 text-center">
            <div id="tanggalku" style="font-size: 24px; color: #2c3e50; font-weight: bold;"></div>
        </div>
        <div class="col-md-3 text-end">
            <h5 class="mb-1"><?= date("l, d F Y") ?></h5>
            <h6 class="total_perjanjian text-muted"><b>Total Perjanjian ()</b></h6>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container-fluid pb-4">
    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card summary-card bg-primary text-white">
                <div class="card-body text-center">
                    <h4 class="card-title">Total Perjanjian</h4>
                    <h2 id="total_perjanjian_count">0</h2>
                    <small><i class="fas fa-calendar-check"></i> Total Appointment</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card summary-card bg-success text-white">
                <div class="card-body text-center">
                    <h4 class="card-title">Total Dokter</h4>
                    <h2 id="total_dokter_count">0</h2>
                    <small><i class="fas fa-user-md"></i> Unique Doctors</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card summary-card bg-info text-white">
                <div class="card-body text-center">
                    <h4 class="card-title">Total Ruangan</h4>
                    <h2 id="total_ruangan_count">0</h2>
                    <small><i class="fas fa-door-open"></i> Unique Rooms</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card summary-card bg-warning text-white">
                <div class="card-body text-center">
                    <h4 class="card-title">Jadwal Pagi</h4>
                    <h2 id="total_pagi_count">0</h2>
                    <small><i class="fas fa-sun"></i> 06:00 - 12:00</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Summary Row -->
    <div class="row mb-3">
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h4 class="card-title">Jadwal Sore</h4>
                    <h2 id="total_sore_count">0</h2>
                    <small><i class="fas fa-sun"></i> 12:00 - 18:00</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h4 class="card-title">Jadwal Malam</h4>
                    <h2 id="total_malam_count">0</h2>
                    <small><i class="fas fa-moon"></i> 18:00 - 06:00</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-dark text-white">
                <div class="card-body text-center">
                    <h4 class="card-title">Dengan Jadwal</h4>
                    <h2 id="total_dengan_jadwal_count">0</h2>
                    <small><i class="fas fa-clock"></i> Has Time Slot</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filter Section -->
    <div class="row">
        <div class="col-md-12 mb-3">
            <div class="card">
                <div class="card-body">
                    <form id="filterForm" class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Tanggal Awal</label>
                                <input type="datetime-local" class="form-control" id="tgl_awal" name="tgl_awal" value="<?= date('Y-m-d\T00:00') ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Tanggal Akhir</label>
                                <input type="datetime-local" class="form-control" id="tgl_akhir" name="tgl_akhir" value="<?= date('Y-m-d\T23:59') ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="button" id="btnFilter" class="btn btn-primary form-control">
                                    <i class="fas fa-search"></i> Filter Data
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Data Table -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="perjanjianTable" class="display" style="width:100%">
                            <thead>
                                <tr style="text-align:center">
                                    <th>No</th>
                                    <th>No. RM</th>
                                    <th>Nama Pasien</th>
                                    <th>No. Perjanjian</th>
                                    <th>Dokter</th>
                                    <th>Ruangan</th>
                                    <th>Dibuat Oleh</th>
                                    <th>Penjamin</th>
                                    <th>Jadwal</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Wait for template scripts to load
function initPerjanjianTable() {
    // Check if jQuery and DataTables are available
    if (typeof jQuery === 'undefined') {
        console.log('jQuery not loaded yet, retrying...');
        setTimeout(initPerjanjianTable, 100);
        return;
    }
    
    if (typeof jQuery.fn.DataTable === 'undefined') {
        console.log('DataTables not loaded yet, retrying...');
        setTimeout(initPerjanjianTable, 100);
        return;
    }
    
    $(document).ready(function() {
        console.log('Initializing Perjanjian DataTable...');

        var table = $('#perjanjianTable').DataTable({
            responsive: true,
            processing: true,
            serverSide: false,
            dom: 'Bfrtip',
            buttons: [
                {
                    extend: 'pdfHtml5',
                    text: '<i class="fas fa-file-pdf"></i> Export PDF',
                    className: 'btn btn-danger btn-sm',
                    title: function() {
                        var tglAwal = $('#tgl_awal').val();
                        var tglAkhir = $('#tgl_akhir').val();
                        var periode = '';
                        
                        if (tglAwal && tglAkhir) {
                            var startDate = new Date(tglAwal).toLocaleDateString('id-ID');
                            var endDate = new Date(tglAkhir).toLocaleDateString('id-ID');
                            periode = 'Periode: ' + startDate + ' - ' + endDate;
                        } else {
                            periode = 'Periode: ' + new Date().toLocaleDateString('id-ID');
                        }
                        
                        return 'Data Perjanjian - ' + periode;
                    },
                    orientation: 'landscape',
                    pageSize: 'A4',
                    exportOptions: {
                        columns: ':visible'
                    },
                    customize: function(doc) {
                        doc.defaultStyle.fontSize = 8;
                        doc.styles.tableHeader.fontSize = 9;
                        doc.styles.tableHeader.fillColor = '#2c3e50';
                    }
                },
                {
                    extend: 'excelHtml5',
                    text: '<i class="fas fa-file-excel"></i> Export Excel',
                    className: 'btn btn-success btn-sm',
                    title: function() {
                        var tglAwal = $('#tgl_awal').val();
                        var tglAkhir = $('#tgl_akhir').val();
                        var periode = '';
                        
                        if (tglAwal && tglAkhir) {
                            var startDate = new Date(tglAwal).toLocaleDateString('id-ID');
                            var endDate = new Date(tglAkhir).toLocaleDateString('id-ID');
                            periode = 'Periode: ' + startDate + ' - ' + endDate;
                        } else {
                            periode = 'Periode: ' + new Date().toLocaleDateString('id-ID');
                        }
                        
                        return 'Data Perjanjian - ' + periode;
                    },
                    exportOptions: {
                        columns: ':visible'
                    }
                }
            ],
            ajax: {
                url: 'Perjanjian/get_perjanjian_data',
                type: 'POST',
                data: function(d) {
                    d.tgl_awal = $('#tgl_awal').val();
                    d.tgl_akhir = $('#tgl_akhir').val();
                    console.log('Sending data:', d);
                },
                dataSrc: function(json) {
                    console.log('DataTable response:', json);
                    if (json.error) {
                        console.error('Server error:', json.error);
                        alert('Error: ' + json.error);
                        return [];
                    }
                    
                    // Update total count
                    if (json.recordsTotal !== undefined) {
                        $(".total_perjanjian").text('Total Perjanjian (' + json.recordsTotal + ')');
                        loadSummaryData();
                    }
                    
                    return json.data || [];
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', status, error);
                    console.error('Response:', xhr.responseText);
                    alert('AJAX Error: ' + error + '\nCheck console for details');
                }
            },
            columnDefs: [
                {
                    targets: [0, 3, 5, 7], // Center align numeric columns, No. Perjanjian, Ruangan, and PENJAMIN
                    className: 'text-center'
                },
                {
                    targets: [8], // Jadwal column
                    render: function(data, type, row) {
                        if (data && data.includes('[') && data.includes(']')) {
                            // Extract time from [HH:MM - HH:MM] format
                            var timeMatch = data.match(/\[(\d{2}):(\d{2})\s*-\s*(\d{2}):(\d{2})\]/);
                            if (timeMatch) {
                                var startHour = parseInt(timeMatch[1]);
                                var badgeClass = 'bg-secondary';

                                if (startHour >= 6 && startHour < 12) {
                                    badgeClass = 'bg-warning'; // Morning
                                } else if (startHour >= 12 && startHour < 18) {
                                    badgeClass = 'bg-info'; // Afternoon
                                } else {
                                    badgeClass = 'bg-dark'; // Evening/Night
                                }

                                return '<span class="badge ' + badgeClass + ' status-badge">' + data + '</span>';
                            }
                        }
                        return data || '';
                    },
                    className: 'text-center'
                }
            ],
            ordering: true,
            searching: true,
            lengthChange: true,
            pageLength: 100,
            lengthMenu: [25, 50, 100, 200],
            order: [[0, 'asc']], // Order by number ascending
            initComplete: function(settings, json) {
                console.log('DataTable initialization complete');
            },
            drawCallback: function(settings) {
                console.log('DataTable draw callback triggered');
            }
        });

        $('#btnFilter').on('click', function() {
            console.log('Filter button clicked');
            table.ajax.reload();
        });

        function loadSummaryData() {
            console.log('Loading summary data...');
            $.ajax({
                url: 'Perjanjian/get_summary_data',
                type: 'POST',
                data: {
                    tgl_awal: $('#tgl_awal').val(),
                    tgl_akhir: $('#tgl_akhir').val()
                },
                success: function(response) {
                    console.log('Summary response:', response);
                    try {
                        var data = JSON.parse(response);
                        $('#total_perjanjian_count').text(data.total_perjanjian || 0);
                        $('#total_dokter_count').text(data.total_dokter || 0);
                        $('#total_ruangan_count').text(data.total_ruangan || 0);
                        $('#total_pagi_count').text(data.total_pagi || 0);
                        $('#total_sore_count').text(data.total_sore || 0);
                        $('#total_malam_count').text(data.total_malam || 0);
                        $('#total_dengan_jadwal_count').text(data.total_dengan_jadwal || 0);
                    } catch (e) {
                        console.error('Error parsing summary response:', e);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Summary AJAX Error:', status, error);
                }
            });
        }

        function reloadDatatable() {
            $('#perjanjianTable').DataTable().ajax.reload(null, false);
        }
        
        setInterval(reloadDatatable, 300000); // Refresh every 5 minutes

        // Clock function - only initialize if element exists and no other clock is running
        if (document.getElementById("tanggalku") && !window.clockInitialized) {
            window.clockInitialized = true;
            
            function updateClock() {
                var tanggal = new Date();
                var clockElement = document.getElementById("tanggalku");
                if (clockElement) {
                    var hours = tanggal.getHours().toString().padStart(2, '0');
                    var minutes = tanggal.getMinutes().toString().padStart(2, '0');
                    var seconds = tanggal.getSeconds().toString().padStart(2, '0');
                    clockElement.innerHTML = hours + " : " + minutes + " : " + seconds;
                }
            }
            
            // Update immediately and then every second
            updateClock();
            setInterval(updateClock, 1000);
        }
    });
}

// Initialize the perjanjian table
initPerjanjianTable();
</script>
