<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Pulang extends CI_Controller {

    function __construct()
    {
        parent::__construct();
        date_default_timezone_set("Asia/Bangkok");
    }

	public function index()
	{
		// $data['total'] = $this->Model_pulang->hitung();
		$this->load->view('v_pulang');
	}

    public function get_data_pasien(){
		$draw   = intval($this->input->POST("draw"));
		$start  = intval($this->input->POST("start"));
		$length = intval($this->input->POST("length"));
	
		$listpulang = $this->Model_pulang->listPasien();
	
		$data = array();
		$no = 1;
		foreach ($listpulang->result() as $LP) {
			$tg1 = strtotime(date('Y-m-d H:i:s'));
			$tg2 = strtotime(date('Y-m-d H:i:s', strtotime($LP->TGLKUNJUNGAN)));
			$kurangin = $tg1-$tg2;

			$a = strtotime(date('Y-m-d H:i:s', strtotime($LP->TGLORDERLAB_PK)));
			$b = strtotime(date('Y-m-d H:i:s', strtotime($LP->TGL_HASIL_PK)));
			$kurang = $b-$a;

			$jamorder = strtotime(date('Y-m-d H:i:s', strtotime($LP->TANGGAL_ORDER_RADIOLOGI)));
			$jamhasil = strtotime(date('Y-m-d H:i:s', strtotime($LP->TGL_HASIL_FINAL_EKSPERTISE)));
			$kurangi = $jamhasil-$jamorder;
			
			$jamorder_resep = strtotime(date('Y-m-d H:i:s', strtotime($LP->TANGGAL_ORDER_RESEP)));
			$jamhasil_resep = strtotime(date('Y-m-d H:i:s', strtotime($LP->TANGGAL_FINAL_RESEP)));
			$hasil = $jamhasil_resep-$jamorder_resep;

			$jam   = floor($kurangin / (60 * 60));
			$menit = $kurangin - $jam * (60 * 60);

			$jam_lab   = floor($kurang / (60 * 60));
			$menit_lab = $kurang - $jam_lab * (60 * 60);

			$jam_rad   = floor($kurangi / (60 * 60));
			$menit_rad = $kurangi - $jam_rad * (60 * 60);
			
			$jam_far   = floor($hasil / (60 * 60));
			$menit_far = $hasil - $jam_far * (60 * 60);

			if($jam >= "12"){
				$level = 'merah';
			}elseif($jam >= "9" && $jam < "12"){
				$level = 'orange';
			}elseif($jam >="6" && $jam < "9"){
				$level = 'kuning';
			}else{
				$level = 'hijau';
			}

            $data[] = array(
				$LP->NORM !=="" ? "<b>$LP->NORM</b>" : "",
                $LP->NAMALENGKAP,
                $LP->DIAGNOSA_MEDIS_UTAMA !="" ? "$LP->DIAGNOSA_MEDIS_UTAMA" : "-",
				$LP->DIAGNOSA_MEDIS_SEKUNDER !="" ? "$LP->DIAGNOSA_MEDIS_SEKUNDER" : "-",
                $LP->PERMINTAAN_RAWAT !="" ? "$LP->PERMINTAAN_RAWAT" : "-",
                $LP->JAWABAN_ADMISSION !="" ? "$LP->JAWABAN_ADMISSION" : "-",
				$LP->ASAL !="" ? "$LP->ASAL" : "Luar RSKD",
                $LP->TGLKUNJUNGAN,
                $LP->DILAB !="" ? "$jam_lab" . " Jam ". floor( $menit_lab / 60 ) . " menit " : "-",
				// $LP->DIRADIOLOGI !="" ? "$jam_rad" . " Jam ". floor( $menit_rad / 60 ) . " menit " : "-",
				// $LP->DIFARMASI !="" ? "$jam_far" . " Jam ". floor( $menit_far / 60 ) . " menit " : "-",
				$jam .  ' jam ' . floor( $menit / 60 ) . ' menit',
				$level,
				$LP->DPJP,
				$LP->TOTAL,
				// $LP->HASIL_PCR !="" ? "$LP->HASIL_PCR" : "-",
				$LP->TANGGAL_PENGKAJIAN
            );

		}
	
		$output = array(
            "draw"            => $draw,
            "recordsTotal"    => $listpulang->num_rows(),
            "recordsFiltered" => $listpulang->num_rows(),
            "data"            => $data
		);
		echo json_encode($output);
	}
}
